# 盘搜前端项目（pan-so-frontend）使用说明

## 项目简介

本项目是基于 Next.js 的网盘资源聚合搜索平台前端，支持百度网盘、夸克网盘、阿里云盘、迅雷网盘等多种资源的聚合展示与搜索。界面简洁，交互友好，适合新手和有经验的开发者二次开发。

---

## 目录结构说明

```
├── src/                # 前端主代码目录
│   ├── app/            # Next.js 路由与页面（含全局样式、主入口、搜索页、教程页等）
│   ├── components/     # 复用组件（如资源卡片、导航栏、分页、UI库等）
│   ├── data/           # 静态数据与教程文档
│   ├── store/          # 状态管理（如 loading 状态）
│   ├── services/       # API 请求封装
│   ├── lib/            # 工具函数
│   ├── hooks/          # 自定义 React Hooks
│   ├── contexts/       # React 上下文（如主题）
│   └── config/         # 常量与配置
├── public/             # 静态资源（图片、svg、robots.txt、教程md等）
├── scripts/            # Node.js 脚本（如通知重置、更新）
├── pages/              # 兼容旧版 Next.js 路由（如 debug 页面、资源详情页）
├── tailwind.config.js  # Tailwind CSS 配置
├── package.json        # 项目依赖与脚本
└── README.md           # 项目说明文档
```

---

## 主要功能模块

- **资源搜索与聚合**：支持多网盘资源的聚合搜索与展示。
- **资源卡片**：美观的资源卡片组件，支持标签、复制、打开链接、失效反馈等功能。
- **教程中心**：内置新手教程、资源分享指南等，帮助用户快速上手。
- **通知系统**：支持公告、通知推送。
- **主题切换**：支持明暗主题自动切换。
- **分页与导航**：便捷的页面跳转与导航栏。
- **API 封装**：统一的后端接口调用方式，便于维护和扩展。

---

## 本地开发与运行

1. **安装依赖**

   ```bash
   npm install
   # 或
   yarn install
   ```

2. **启动开发服务器**

   ```bash
   npm run dev
   # 或
   yarn dev
   ```

   启动后访问 [http://localhost:3000](http://localhost:3000)

3. **构建生产环境**

   ```bash
   npm run build
   npm start
   ```

---

## 重要文件与目录详解

- `src/app/`：Next.js 13+ 的新路由系统，所有页面都在这里维护。
  - `globals.css`：全局样式，基于 Tailwind CSS。
  - `search/`：资源搜索主页面。
  - `tutorials/`：新手教程页面及详情。
- `src/components/`：所有可复用的 UI 组件。
  - `ResourceCard.tsx`：资源卡片主组件。
  - `ui/`：通用 UI 组件（如 Button、Input）。
  - `layout/`：页面布局相关组件。
- `src/data/`：静态教程、markdown 文档、json 数据等。
- `src/services/`：API 请求封装，统一管理后端接口。
- `src/store/`：全局状态管理（如 loading 状态）。
- `public/`：静态资源目录，图片、svg、robots.txt、教程 md 等。
- `scripts/`：Node.js 脚本，辅助数据维护。
- `pages/`：兼容旧版 Next.js 路由，部分特殊页面。

---

## 常见问题

1. **样式不生效？**

   - 请确保已正确安装并编译 Tailwind CSS，且页面引用了最新的 CSS 文件。
   - 修改 `tailwind.config.js` 后需重启开发服务器或重新编译 CSS。

2. **接口请求失败？**

   - 检查后端服务是否启动，接口地址是否正确。
   - 可在 `src/services/` 里调整 API 地址。

3. **如何添加新教程？**

   - 在 `src/data/` 目录下添加 markdown 文件，并在 `tutorials.json` 里配置即可。

4. **如何自定义主题色？**

   - 修改 `tailwind.config.js` 的 `extend.colors`，并在组件中使用自定义类名。

5. **如何配置链接处理行为？**
   - 通过环境变量控制是否调用 `get_share` 接口，详见 `docs/link-config.md`。
   - 示例配置文件：`.env.example`。

---

## 链接处理配置

前端支持通过环境变量配置复制链接和进入网盘按钮的行为：

- `NEXT_PUBLIC_ENABLE_GET_SHARE_API`: 是否启用 get_share 接口（默认: true）
- `NEXT_PUBLIC_GET_SHARE_TYPES`: 指定哪些网盘类型使用 get_share 接口（默认: baidu,quark）

详细配置说明请参考 [`docs/link-config.md`](docs/link-config.md)。

---

## 参与贡献

欢迎提交 issue 和 PR，完善功能与文档！

---

如需更详细的开发文档或遇到其他问题，欢迎在 issues 区留言。

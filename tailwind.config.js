/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  darkMode: ["class"],
  theme: {
    extend: {
      screens: {
        xs: "320px",
      },
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        "secondary-text": "var(--secondary-text)",
        "nav-background": "var(--nav-background)",
        "card-background": "var(--card-background)",
        "hover-background": "var(--hover-background)",
        "border-color": "var(--border-color)",
        "link-color": "var(--link-color)",
        "button-background": "var(--button-background)",
        "button-hover": "var(--button-hover)",
        "quark-primary": "#00bcd4",
      },
      fontFamily: {
        sans: [
          "-apple-system",
          "BlinkMacSystemFont",
          '"SF Pro Text"',
          '"Helvetica Neue"',
          "Arial",
          "sans-serif",
        ],
        display: [
          '"SF Pro Display"',
          "-apple-system",
          "BlinkMacSystemFont",
          "sans-serif",
        ],
      },
      boxShadow: {
        apple: "0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)",
        "apple-hover":
          "0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22)",
      },
    },
  },
  safelist: [
    "bg-quark-primary",
    "bg-quark-primary/10",
    "bg-quark-primary/20",
    "hover:bg-quark-primary/90",
    "text-quark-primary",
  ],
  plugins: [],
};

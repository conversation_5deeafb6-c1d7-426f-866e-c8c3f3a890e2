# 环境变量配置示例文件
# 复制此文件为 .env.local 并根据需要修改配置

# API 基础地址
NEXT_PUBLIC_API_BASE_URL=https://your-api-domain.com

# 开发环境 API 代理目标（仅在开发环境使用）
API_PROXY_TARGET=http://127.0.0.1:9999

# ===== 链接处理配置 =====

# 是否启用 get_share 接口调用
# true: 调用 get_share 接口获取最新链接（默认）
# false: 仅调用 check_resource_status 接口，直接使用返回的 share_url
NEXT_PUBLIC_ENABLE_GET_SHARE_API=true

# 指定哪些网盘类型使用 get_share 接口
# 格式：用逗号分隔的网盘类型列表
# 可选值：baidu, quark, thunder, aliyun
# 默认：baidu,quark（百度网盘和夸克网盘使用 get_share 接口）
NEXT_PUBLIC_GET_SHARE_TYPES=baidu,quark

# ===== 配置说明 =====
# 
# 1. 完全禁用 get_share 接口（所有网盘类型都只调用 check_resource_status）：
#    NEXT_PUBLIC_ENABLE_GET_SHARE_API=false
#
# 2. 只有百度网盘使用 get_share 接口：
#    NEXT_PUBLIC_ENABLE_GET_SHARE_API=true
#    NEXT_PUBLIC_GET_SHARE_TYPES=baidu
#
# 3. 所有网盘类型都使用 get_share 接口：
#    NEXT_PUBLIC_ENABLE_GET_SHARE_API=true
#    NEXT_PUBLIC_GET_SHARE_TYPES=baidu,quark,thunder,aliyun
#
# 4. 完全禁用 get_share 接口，所有按钮都直接使用 check_resource_status 返回的链接：
#    NEXT_PUBLIC_ENABLE_GET_SHARE_API=false
#    # NEXT_PUBLIC_GET_SHARE_TYPES 在此情况下会被忽略

"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { 
  getCurrentUser, 
  isAuthenticated, 
  logout as authLogout,
  User 
} from "@/services/authService";
import { useToast } from "@/components/ToastProvider";

interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

export function useAuth() {
  const router = useRouter();
  const { showToast } = useToast();
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isLoading: true,
    isAuthenticated: false,
  });

  // 检查认证状态
  const checkAuth = useCallback(async () => {
    try {
      if (!isAuthenticated()) {
        setAuthState({
          user: null,
          isLoading: false,
          isAuthenticated: false,
        });
        return;
      }

      const user = await getCurrentUser();
      if (user) {
        setAuthState({
          user,
          isLoading: false,
          isAuthenticated: true,
        });
      } else {
        // 如果获取用户信息失败，清除认证状态
        setAuthState({
          user: null,
          isLoading: false,
          isAuthenticated: false,
        });
      }
    } catch (error) {
      console.error("检查认证状态失败:", error);
      setAuthState({
        user: null,
        isLoading: false,
        isAuthenticated: false,
      });
    }
  }, []);

  // 登出
  const logout = useCallback(async () => {
    try {
      const result = await authLogout();
      if (result.success) {
        showToast(result.message, "success");
      }
    } catch (error) {
      console.error("登出失败:", error);
    } finally {
      setAuthState({
        user: null,
        isLoading: false,
        isAuthenticated: false,
      });
      router.push("/login");
    }
  }, [router, showToast]);

  // 刷新用户信息
  const refreshUser = useCallback(async () => {
    if (!isAuthenticated()) return;

    try {
      const user = await getCurrentUser();
      if (user) {
        setAuthState(prev => ({
          ...prev,
          user,
        }));
      }
    } catch (error) {
      console.error("刷新用户信息失败:", error);
    }
  }, []);

  // 检查是否有特定权限
  const hasPermission = useCallback((requiredRole: string) => {
    if (!authState.user) return false;
    
    const roleHierarchy: { [key: string]: number } = {
      user: 1,
      moderator: 2,
      admin: 3,
    };

    const userLevel = roleHierarchy[authState.user.role] || 0;
    const requiredLevel = roleHierarchy[requiredRole] || 0;

    return userLevel >= requiredLevel;
  }, [authState.user]);

  // 检查是否为管理员
  const isAdmin = useCallback(() => {
    return authState.user?.role === "admin";
  }, [authState.user]);

  // 检查是否为版主或管理员
  const isModerator = useCallback(() => {
    return authState.user?.role === "moderator" || authState.user?.role === "admin";
  }, [authState.user]);

  // 要求登录
  const requireAuth = useCallback((redirectTo?: string) => {
    if (!authState.isAuthenticated) {
      const currentPath = window.location.pathname + window.location.search;
      const redirect = redirectTo || currentPath;
      router.push(`/login?redirect=${encodeURIComponent(redirect)}`);
      return false;
    }
    return true;
  }, [authState.isAuthenticated, router]);

  // 要求特定权限
  const requirePermission = useCallback((requiredRole: string, redirectTo?: string) => {
    if (!requireAuth(redirectTo)) return false;
    
    if (!hasPermission(requiredRole)) {
      showToast("您没有权限访问此页面", "error");
      router.push("/");
      return false;
    }
    
    return true;
  }, [requireAuth, hasPermission, showToast, router]);

  // 要求管理员权限
  const requireAdmin = useCallback((redirectTo?: string) => {
    return requirePermission("admin", redirectTo);
  }, [requirePermission]);

  // 初始化时检查认证状态
  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  // 监听存储变化（多标签页同步）
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "auth_token") {
        if (!e.newValue) {
          // Token被删除，用户在其他标签页登出
          setAuthState({
            user: null,
            isLoading: false,
            isAuthenticated: false,
          });
        } else {
          // Token被更新，重新检查认证状态
          checkAuth();
        }
      }
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, [checkAuth]);

  return {
    // 状态
    user: authState.user,
    isLoading: authState.isLoading,
    isAuthenticated: authState.isAuthenticated,
    
    // 方法
    logout,
    refreshUser,
    checkAuth,
    
    // 权限检查
    hasPermission,
    isAdmin,
    isModerator,
    
    // 权限要求
    requireAuth,
    requirePermission,
    requireAdmin,
  };
}

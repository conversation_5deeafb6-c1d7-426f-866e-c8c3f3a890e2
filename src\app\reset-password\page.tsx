"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import { useToast } from "@/components/ToastProvider";
import { resetPassword, ResetPasswordRequest } from "@/services/authService";
import PageHeader from "@/components/PageHeader";

export default function ResetPasswordPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { showToast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [token, setToken] = useState<string>("");
  const [formData, setFormData] = useState({
    new_password: "",
    confirm_password: "",
  });

  useEffect(() => {
    const tokenParam = searchParams?.get("token");
    if (!tokenParam) {
      showToast("无效的重置链接", "error");
      router.push("/forgot-password");
      return;
    }
    setToken(tokenParam);
  }, [searchParams, router, showToast]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const validateForm = () => {
    if (!formData.new_password.trim()) {
      showToast("请输入新密码", "error");
      return false;
    }

    if (formData.new_password.length < 6) {
      showToast("密码至少需要6个字符", "error");
      return false;
    }

    if (formData.new_password !== formData.confirm_password) {
      showToast("两次输入的密码不一致", "error");
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (!token) {
      showToast("无效的重置链接", "error");
      return;
    }

    setIsLoading(true);

    try {
      const resetData: ResetPasswordRequest = {
        token,
        new_password: formData.new_password,
        confirm_password: formData.confirm_password,
      };

      const result = await resetPassword(resetData);

      if (result.success) {
        showToast(result.message, "success");
        // 重置成功后跳转到登录页面
        router.push("/login?message=密码重置成功，请使用新密码登录");
      } else {
        showToast(result.message, "error");
        // 如果token无效，跳转到忘记密码页面
        if (
          result.message?.includes("无效") ||
          result.message?.includes("过期")
        ) {
          setTimeout(() => {
            router.push("/forgot-password");
          }, 2000);
        }
      }
    } catch {
      showToast("重置密码失败，请稍后重试", "error");
    } finally {
      setIsLoading(false);
    }
  };

  if (!token) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-button-background mx-auto mb-4"></div>
          <p className="text-secondary-text">验证重置链接...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <PageHeader title="重置密码" description="设置您的新密码" />

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-md mx-auto">
          <div className="bg-card-background rounded-lg shadow-lg p-8 border border-border-color">
            <div className="text-center mb-8">
              <h1 className="text-2xl font-bold text-foreground mb-2">
                重置密码
              </h1>
              <p className="text-secondary-text">请输入您的新密码</p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label
                  htmlFor="new_password"
                  className="block text-sm font-medium text-foreground mb-2"
                >
                  新密码
                </label>
                <Input
                  id="new_password"
                  name="new_password"
                  type="password"
                  value={formData.new_password}
                  onChange={handleInputChange}
                  placeholder="请输入新密码（至少6个字符）"
                  required
                  className="w-full"
                />
              </div>

              <div>
                <label
                  htmlFor="confirm_password"
                  className="block text-sm font-medium text-foreground mb-2"
                >
                  确认新密码
                </label>
                <Input
                  id="confirm_password"
                  name="confirm_password"
                  type="password"
                  value={formData.confirm_password}
                  onChange={handleInputChange}
                  placeholder="请再次输入新密码"
                  required
                  className="w-full"
                />
              </div>

              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg
                      className="h-5 w-5 text-blue-400"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-blue-800 dark:text-blue-200">
                      为了您的账户安全，请设置一个强密码：
                    </p>
                    <ul className="mt-2 text-xs text-blue-700 dark:text-blue-300 list-disc list-inside">
                      <li>至少6个字符</li>
                      <li>建议包含字母、数字和特殊字符</li>
                      <li>避免使用常见密码</li>
                    </ul>
                  </div>
                </div>
              </div>

              <Button
                type="submit"
                disabled={isLoading}
                className="w-full"
                size="lg"
              >
                {isLoading ? "重置中..." : "重置密码"}
              </Button>
            </form>

            <div className="mt-8 text-center">
              <Link
                href="/login"
                className="text-link-color hover:text-button-hover font-medium transition-colors"
              >
                ← 返回登录
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

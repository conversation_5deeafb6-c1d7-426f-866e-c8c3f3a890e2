"use client";

import { useAuth } from "@/hooks/useAuth";
import { Button } from "@/components/ui/Button";
import PageHeader from "@/components/PageHeader";
import AuthGuard from "@/components/AuthGuard";

function AuthTestContent() {
  const { user, isAuthenticated, logout, isAdmin, isModerator, refreshUser } = useAuth();

  return (
    <div className="min-h-screen bg-background">
      <PageHeader 
        title="认证测试页面"
        description="测试用户认证功能"
      />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="bg-card-background rounded-lg shadow-lg p-8 border border-border-color">
            <h2 className="text-xl font-semibold text-foreground mb-6">认证状态</h2>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-hover-background rounded-lg">
                <span className="text-foreground">登录状态:</span>
                <span className={`font-medium ${isAuthenticated ? 'text-green-600' : 'text-red-600'}`}>
                  {isAuthenticated ? '已登录' : '未登录'}
                </span>
              </div>

              {isAuthenticated && user && (
                <>
                  <div className="flex items-center justify-between p-4 bg-hover-background rounded-lg">
                    <span className="text-foreground">用户名:</span>
                    <span className="font-medium text-foreground">{user.username}</span>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-hover-background rounded-lg">
                    <span className="text-foreground">邮箱:</span>
                    <span className="font-medium text-foreground">{user.email}</span>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-hover-background rounded-lg">
                    <span className="text-foreground">角色:</span>
                    <span className="font-medium text-foreground">{user.role}</span>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-hover-background rounded-lg">
                    <span className="text-foreground">状态:</span>
                    <span className="font-medium text-foreground">{user.status}</span>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-hover-background rounded-lg">
                    <span className="text-foreground">是否管理员:</span>
                    <span className={`font-medium ${isAdmin() ? 'text-green-600' : 'text-gray-600'}`}>
                      {isAdmin() ? '是' : '否'}
                    </span>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-hover-background rounded-lg">
                    <span className="text-foreground">是否版主:</span>
                    <span className={`font-medium ${isModerator() ? 'text-green-600' : 'text-gray-600'}`}>
                      {isModerator() ? '是' : '否'}
                    </span>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-hover-background rounded-lg">
                    <span className="text-foreground">注册时间:</span>
                    <span className="font-medium text-foreground">
                      {new Date(user.created_at).toLocaleString('zh-CN')}
                    </span>
                  </div>

                  {user.last_login && (
                    <div className="flex items-center justify-between p-4 bg-hover-background rounded-lg">
                      <span className="text-foreground">最后登录:</span>
                      <span className="font-medium text-foreground">
                        {new Date(user.last_login).toLocaleString('zh-CN')}
                      </span>
                    </div>
                  )}
                </>
              )}
            </div>

            <div className="mt-8 space-y-4">
              <h3 className="text-lg font-semibold text-foreground">操作</h3>
              
              <div className="flex flex-wrap gap-4">
                <Button onClick={refreshUser}>
                  刷新用户信息
                </Button>
                
                {isAuthenticated && (
                  <Button onClick={logout} variant="outline">
                    登出
                  </Button>
                )}
              </div>
            </div>

            <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
                测试说明
              </h4>
              <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
                <li>• 此页面需要登录才能访问</li>
                <li>• 显示当前用户的详细信息</li>
                <li>• 测试权限检查功能</li>
                <li>• 验证认证状态管理</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function AuthTestPage() {
  return (
    <AuthGuard requireAuth={true}>
      <AuthTestContent />
    </AuthGuard>
  );
}

import { useState } from "react";
import { useToast } from "@/components/ToastProvider";

type ClipboardType = "baiduLink" | "quarkLink" | "thunderLink" | "aliyunLink";

interface CopiedState {
  baiduLink: boolean;
  quarkLink: boolean;
  thunderLink: boolean;
  aliyunLink: boolean;
}

/**
 * 自定义Hook：复制功能
 * 提供多种复制方法和回退机制
 */
export const useClipboard = () => {
  const [copied, setCopied] = useState<CopiedState>({
    baiduLink: false,
    quarkLink: false,
    thunderLink: false,
    aliyunLink: false,
  });
  const { showToast } = useToast();

  const copyToClipboard = (text: string, type: ClipboardType) => {
    // 尝试使用现代剪贴板API
    const copyWithClipboardAPI = async (): Promise<boolean> => {
      try {
        await navigator.clipboard.writeText(text);
        setCopied((prev) => ({ ...prev, [type]: true }));
        setTimeout(() => {
          setCopied((prev) => ({ ...prev, [type]: false }));
        }, 2000);
        showToast("链接已复制到剪贴板", "success");
        return true;
      } catch {
        return false;
      }
    };

    // 回退到execCommand
    const copyWithExecCommand = (): boolean => {
      try {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.position = "fixed";
        textArea.style.left = "-999999px";
        textArea.style.top = "-999999px";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        const success = document.execCommand("copy");
        document.body.removeChild(textArea);
        if (success) {
          setCopied((prev) => ({ ...prev, [type]: true }));
          setTimeout(() => {
            setCopied((prev) => ({ ...prev, [type]: false }));
          }, 2000);
          showToast("链接已复制到剪贴板", "success");
          return true;
        }
        return false;
      } catch {
        return false;
      }
    };

    // 如果自动复制失败，则显示链接让用户手动复制
    const showManualCopyUI = () => {
      // 创建弹窗容器
      const modalContainer = document.createElement("div");
      modalContainer.style.position = "fixed";
      modalContainer.style.top = "0";
      modalContainer.style.left = "0";
      modalContainer.style.width = "100%";
      modalContainer.style.height = "100%";
      modalContainer.style.backgroundColor = "rgba(0, 0, 0, 0.5)";
      modalContainer.style.display = "flex";
      modalContainer.style.justifyContent = "center";
      modalContainer.style.alignItems = "center";
      modalContainer.style.zIndex = "9999";

      // 创建弹窗内容
      const modalContent = document.createElement("div");
      modalContent.style.backgroundColor = "white";
      modalContent.style.padding = "20px";
      modalContent.style.borderRadius = "5px";
      modalContent.style.maxWidth = "90%";
      modalContent.style.width = "500px";
      modalContent.style.boxShadow = "0 2px 10px rgba(0, 0, 0, 0.2)";

      // 创建标题
      const title = document.createElement("h3");
      title.textContent = "无法自动复制，请手动复制链接:";
      title.style.marginTop = "0";
      title.style.marginBottom = "15px";
      title.style.fontSize = "16px";

      // 创建输入框
      const input = document.createElement("input");
      input.value = text;
      input.style.width = "100%";
      input.style.padding = "8px";
      input.style.marginBottom = "15px";
      input.style.border = "1px solid #ddd";
      input.style.borderRadius = "4px";
      input.style.fontSize = "14px";

      // 创建按钮容器
      const buttonContainer = document.createElement("div");
      buttonContainer.style.display = "flex";
      buttonContainer.style.justifyContent = "flex-end";

      // 创建关闭按钮
      const closeButton = document.createElement("button");
      closeButton.textContent = "关闭";
      closeButton.style.padding = "6px 12px";
      closeButton.style.backgroundColor = "#f3f4f6";
      closeButton.style.color = "#374151";
      closeButton.style.border = "none";
      closeButton.style.borderRadius = "4px";
      closeButton.style.cursor = "pointer";
      closeButton.style.fontSize = "14px";

      // 组装弹窗
      buttonContainer.appendChild(closeButton);
      modalContent.appendChild(title);
      modalContent.appendChild(input);
      modalContent.appendChild(buttonContainer);
      modalContainer.appendChild(modalContent);
      document.body.appendChild(modalContainer);

      // 选中输入框内容
      input.focus();
      input.select();

      // 关闭按钮点击事件
      closeButton.onclick = () => {
        document.body.removeChild(modalContainer);
      };

      // 点击背景关闭
      modalContainer.onclick = (e) => {
        if (e.target === modalContainer) {
          document.body.removeChild(modalContainer);
        }
      };

      // 自动关闭
      setTimeout(() => {
        if (document.body.contains(modalContainer)) {
          document.body.removeChild(modalContainer);
        }
      }, 10000);
    };

    // 尝试多种复制方法，按照优先级
    (async () => {
      // 尝试方法1: 使用Clipboard API
      const clipboardSuccess = await copyWithClipboardAPI();
      if (clipboardSuccess) return;

      // 尝试方法2: 使用execCommand
      const execCommandSuccess = copyWithExecCommand();
      if (execCommandSuccess) return;

      // 如果都失败，显示手动复制界面
      showManualCopyUI();
    })();
  };

  return { copied, copyToClipboard };
};

import { useState, useEffect } from 'react';
import Head from 'next/head';

export default function ResourceDetail({ resource }) {
    const [showFeedbackModal, setShowFeedbackModal] = useState(false);

    // 处理反馈表单提交完成
    const handleReportSubmitted = (event) => {
        if (event.data && event.data.type === 'resource-report-submitted') {
            setTimeout(() => {
                setShowFeedbackModal(false);
            }, 1500);
        }
    };

    // 添加窗口消息监听器
    useEffect(() => {
        window.addEventListener('message', handleReportSubmitted, { passive: true });
        return () => {
            window.removeEventListener('message', handleReportSubmitted);
        };
    }, []);

    // 确保resource存在，防止构建错误
    if (!resource) {
        return <div className="container mx-auto px-4 py-8">资源不存在或已被删除</div>;
    }

    return (
        <div className="container mx-auto px-4 py-8">
            <Head>
                <meta name="robots" content="noindex, nofollow" />
            </Head>
            <div className="bg-white shadow rounded-lg p-6 mb-6">
                <h1 className="text-2xl font-bold mb-4">{resource.title}</h1>

                <div className="flex justify-between items-center mb-4">
                    <div>
                        {/* ... existing code if any ... */}
                    </div>
                    <button
                        onClick={() => setShowFeedbackModal(true)}
                        className="text-xs bg-red-50 text-red-600 px-2 py-0.5 rounded hover:bg-red-100 transition-colors"
                    >
                        报告失效
                    </button>
                </div>

                {/* ... existing code ... */}
            </div>

            {showFeedbackModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg w-full max-w-md mx-4 overflow-hidden">
                        <div className="p-4 border-b flex justify-between items-center">
                            <h3 className="font-medium">资源失效反馈</h3>
                            <button
                                onClick={() => setShowFeedbackModal(false)}
                                className="text-gray-500 hover:text-gray-700"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        <div className="h-96">
                            <iframe
                                src={`/report-form.html?id=${resource.id}&name=${encodeURIComponent(resource.title)}`}
                                className="w-full h-full border-0"
                                title="资源失效反馈"
                            ></iframe>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}

// 预渲染的路径
export async function getStaticPaths() {
    // 由于resources.json已被删除，返回空路径
    return {
        paths: [],
        fallback: 'blocking', // 可以设置为true或'blocking'
    };
}

// 预渲染页面的内容
export async function getStaticProps({ }) {
    // 由于resources.json已被删除，返回404
    return {
        notFound: true,
    };
} 

// 对内获取数据时，使用您指定的后端服务地址
// 这是用于服务器到服务器的通信
const BACKEND_API_URL = process.env.API_PROXY_TARGET;

// 将这个函数导出，以便其他服务器端代码（如 sitemap.ts）可以直接调用它
export async function getTotalResourceCountFromBackend(): Promise<number> {
  if (!BACKEND_API_URL) {
    console.error("错误：环境变量 API_PROXY_TARGET 未设置。无法获取资源总数。");
    return 0;
  }

  try {
    const response = await fetch(`${BACKEND_API_URL}/api/resources/count`, {
      // Next.js 的服务端缓存策略:
      // revalidate: 36000 表示将结果缓存10小时
      // 这可以有效减轻您后端API的压力
      next: { revalidate: 36000 },
    });

    if (!response.ok) {
      console.error(`获取资源总数失败，后端接口状态: ${response.status}`);
      return 0;
    }

    const data = await response.json();

    // 从后端返回的 { "count": ... } 中提取数值
    return data.count || 0;
  } catch (error) {
    console.error("调用后端获取资源总数接口时发生网络错误:", error);
    // 发生错误时返回0，防止站点地图生成过程完全中断
    return 0;
  }
}

import { NextResponse } from "next/server";
// import { getTotalResourceCountFromBackend as getTotalResourceCount } from "@/lib/resource";

const PUBLIC_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "https://pansoo.cn";
// const RESOURCES_PER_SITEMAP = 5000;

function generateSitemapIndex(): string {
  let sitemapIndex = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

  // 添加静态页面 sitemap
  sitemapIndex += `
  <sitemap>
    <loc>${PUBLIC_URL}/sitemap-static.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>`;

  // 注释掉动态资源 sitemap
  /* 
  // 添加动态资源 sitemap
  for (let i = 0; i < totalPages; i++) {
    sitemapIndex += `
  <sitemap>
    <loc>${PUBLIC_URL}/sitemap-resources/${i + 1}.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>`;
  }
  */

  sitemapIndex += `
</sitemapindex>`;
  return sitemapIndex;
}

export async function GET() {
  // 注释掉动态资源站点地图生成
  // const totalResources = await getTotalResourceCount();
  // const totalPages = Math.ceil(totalResources / RESOURCES_PER_SITEMAP);
  const sitemapIndexXml = generateSitemapIndex();

  return new NextResponse(sitemapIndexXml, {
    headers: { "Content-Type": "application/xml" },
  });
}

import PageHeader from "@/components/PageHeader";

export default function TermsPage() {
  return (
    <div className="min-h-screen bg-background">
      <PageHeader 
        title="用户协议"
        description="97盘搜用户服务协议"
      />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-card-background rounded-lg shadow-lg p-8 border border-border-color">
            <div className="prose prose-gray dark:prose-invert max-w-none">
              <h2 className="text-xl font-semibold text-foreground mb-4">1. 服务条款的接受</h2>
              <p className="text-secondary-text mb-4">
                欢迎使用97盘搜！通过访问和使用本网站，您同意遵守以下服务条款。如果您不同意这些条款，请不要使用本服务。
              </p>

              <h2 className="text-xl font-semibold text-foreground mb-4">2. 服务描述</h2>
              <p className="text-secondary-text mb-4">
                97盘搜是一个网盘资源搜索平台，为用户提供网盘资源的搜索和分享服务。我们不存储任何文件内容，仅提供资源链接的索引服务。
              </p>

              <h2 className="text-xl font-semibold text-foreground mb-4">3. 用户责任</h2>
              <ul className="list-disc list-inside text-secondary-text mb-4 space-y-2">
                <li>您必须年满18岁或在法定监护人同意下使用本服务</li>
                <li>您有责任保护您的账户信息和密码安全</li>
                <li>您不得使用本服务进行任何非法活动</li>
                <li>您不得上传或分享侵犯他人版权的内容</li>
                <li>您不得恶意攻击或干扰本服务的正常运行</li>
              </ul>

              <h2 className="text-xl font-semibold text-foreground mb-4">4. 内容政策</h2>
              <p className="text-secondary-text mb-4">
                我们禁止分享以下类型的内容：
              </p>
              <ul className="list-disc list-inside text-secondary-text mb-4 space-y-2">
                <li>侵犯版权的内容</li>
                <li>非法、有害或恶意的内容</li>
                <li>色情、暴力或其他不当内容</li>
                <li>垃圾信息或广告内容</li>
              </ul>

              <h2 className="text-xl font-semibold text-foreground mb-4">5. 免责声明</h2>
              <p className="text-secondary-text mb-4">
                本服务按"现状"提供，我们不对服务的可用性、准确性或完整性做出任何保证。用户使用本服务的风险由用户自行承担。
              </p>

              <h2 className="text-xl font-semibold text-foreground mb-4">6. 服务变更</h2>
              <p className="text-secondary-text mb-4">
                我们保留随时修改或终止服务的权利，恕不另行通知。我们也保留随时修改这些服务条款的权利。
              </p>

              <h2 className="text-xl font-semibold text-foreground mb-4">7. 联系我们</h2>
              <p className="text-secondary-text mb-4">
                如果您对这些服务条款有任何疑问，请通过官方QQ群联系我们。
              </p>

              <div className="mt-8 pt-4 border-t border-border-color">
                <p className="text-sm text-secondary-text">
                  最后更新时间：2025年7月9日
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

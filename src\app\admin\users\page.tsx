"use client";

import { useState, useEffect, useCallback } from "react";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import { useToast } from "@/components/ToastProvider";
import {
  getUserList,
  createUser,
  updateUser,
  deleteUser,
  resetUserPassword,
  User,
  UserListResponse,
} from "@/services/authService";
import PageHeader from "@/components/PageHeader";
import { AdminGuard } from "@/components/AuthGuard";

interface UserFormData {
  username: string;
  email: string;
  password: string;
  role: string;
}

export default function UserManagementPage() {
  const { showToast } = useToast();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalUsers, setTotalUsers] = useState(0);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newUserData, setNewUserData] = useState<UserFormData>({
    username: "",
    email: "",
    password: "",
    role: "user",
  });

  const loadUsers = useCallback(async () => {
    setLoading(true);
    try {
      const result: UserListResponse = await getUserList(
        currentPage,
        20,
        searchTerm || undefined,
        roleFilter || undefined,
        statusFilter || undefined
      );

      if (result.success) {
        setUsers(result.data.users);
        setTotalPages(result.data.total_pages);
        setTotalUsers(result.data.total);
      } else {
        showToast(result.message || "获取用户列表失败", "error");
      }
    } catch {
      showToast("获取用户列表失败", "error");
    } finally {
      setLoading(false);
    }
  }, [currentPage, searchTerm, roleFilter, statusFilter, showToast]);

  useEffect(() => {
    loadUsers();
  }, [loadUsers]);

  const handleSearch = () => {
    setCurrentPage(1);
    loadUsers();
  };

  const handleCreateUser = async () => {
    if (!newUserData.username || !newUserData.email || !newUserData.password) {
      showToast("请填写所有必填字段", "error");
      return;
    }

    try {
      const result = await createUser(newUserData);
      if (result.success) {
        showToast(result.message, "success");
        setShowCreateModal(false);
        setNewUserData({ username: "", email: "", password: "", role: "user" });
        loadUsers();
      } else {
        showToast(result.message, "error");
      }
    } catch {
      showToast("创建用户失败", "error");
    }
  };

  const handleUpdateUser = async (userId: number, updates: Partial<User>) => {
    try {
      const result = await updateUser(userId, updates);
      if (result.success) {
        showToast(result.message, "success");
        loadUsers();
      } else {
        showToast(result.message, "error");
      }
    } catch {
      showToast("更新用户失败", "error");
    }
  };

  const handleDeleteUser = async (userId: number, username: string) => {
    if (!confirm(`确定要删除用户 "${username}" 吗？此操作不可恢复。`)) {
      return;
    }

    try {
      const result = await deleteUser(userId);
      if (result.success) {
        showToast(result.message, "success");
        loadUsers();
      } else {
        showToast(result.message, "error");
      }
    } catch {
      showToast("删除用户失败", "error");
    }
  };

  const handleResetPassword = async (userId: number, username: string) => {
    const newPassword = prompt(`为用户 "${username}" 设置新密码：`);
    if (!newPassword) return;

    if (newPassword.length < 6) {
      showToast("密码至少需要6个字符", "error");
      return;
    }

    try {
      const result = await resetUserPassword(userId, newPassword);
      if (result.success) {
        showToast(result.message, "success");
      } else {
        showToast(result.message, "error");
      }
    } catch {
      showToast("重置密码失败", "error");
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("zh-CN");
  };

  const getRoleText = (role: string) => {
    const roleMap: { [key: string]: string } = {
      admin: "管理员",
      user: "普通用户",
      moderator: "版主",
    };
    return roleMap[role] || role;
  };

  const getStatusText = (status: string) => {
    const statusMap: { [key: string]: string } = {
      active: "正常",
      inactive: "未激活",
      suspended: "已冻结",
      banned: "已封禁",
    };
    return statusMap[status] || status;
  };

  const getStatusColor = (status: string) => {
    const colorMap: { [key: string]: string } = {
      active: "text-green-600 dark:text-green-400",
      inactive: "text-yellow-600 dark:text-yellow-400",
      suspended: "text-red-600 dark:text-red-400",
      banned: "text-red-800 dark:text-red-300",
    };
    return colorMap[status] || "text-gray-600 dark:text-gray-400";
  };

  return (
    <AdminGuard>
      <div className="min-h-screen bg-background">
        <PageHeader title="用户管理" description="管理系统用户账户" />

        <div className="container mx-auto px-4 py-8">
          {/* 搜索和筛选区域 */}
          <div className="bg-card-background rounded-lg shadow-lg p-6 border border-border-color mb-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  搜索用户
                </label>
                <Input
                  type="text"
                  placeholder="用户名或邮箱"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={(e) => e.key === "Enter" && handleSearch()}
                />
              </div>

              <div>
                <label
                  htmlFor="role-filter"
                  className="block text-sm font-medium text-foreground mb-2"
                >
                  角色筛选
                </label>
                <select
                  id="role-filter"
                  value={roleFilter}
                  onChange={(e) => setRoleFilter(e.target.value)}
                  className="w-full px-4 py-2 rounded-md border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-foreground bg-white dark:bg-[#2c2c34] dark:border-[#4b4d61]"
                >
                  <option value="">全部角色</option>
                  <option value="admin">管理员</option>
                  <option value="moderator">版主</option>
                  <option value="user">普通用户</option>
                </select>
              </div>

              <div>
                <label
                  htmlFor="status-filter"
                  className="block text-sm font-medium text-foreground mb-2"
                >
                  状态筛选
                </label>
                <select
                  id="status-filter"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-4 py-2 rounded-md border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-foreground bg-white dark:bg-[#2c2c34] dark:border-[#4b4d61]"
                >
                  <option value="">全部状态</option>
                  <option value="active">正常</option>
                  <option value="inactive">未激活</option>
                  <option value="suspended">已冻结</option>
                  <option value="banned">已封禁</option>
                </select>
              </div>

              <div className="flex items-end">
                <Button onClick={handleSearch} className="mr-2">
                  搜索
                </Button>
                <Button
                  onClick={() => setShowCreateModal(true)}
                  variant="outline"
                >
                  新增用户
                </Button>
              </div>
            </div>
          </div>

          {/* 用户列表 */}
          <div className="bg-card-background rounded-lg shadow-lg border border-border-color">
            <div className="p-6 border-b border-border-color">
              <h2 className="text-lg font-semibold text-foreground">
                用户列表 ({totalUsers} 个用户)
              </h2>
            </div>

            {loading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-button-background mx-auto mb-4"></div>
                <p className="text-secondary-text">加载中...</p>
              </div>
            ) : (
              <>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-hover-background">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-secondary-text uppercase tracking-wider">
                          用户信息
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-secondary-text uppercase tracking-wider">
                          角色
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-secondary-text uppercase tracking-wider">
                          状态
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-secondary-text uppercase tracking-wider">
                          注册时间
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-secondary-text uppercase tracking-wider">
                          最后登录
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-secondary-text uppercase tracking-wider">
                          操作
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-border-color">
                      {users.map((user) => (
                        <tr key={user.id} className="hover:bg-hover-background">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-foreground">
                                {user.username}
                              </div>
                              <div className="text-sm text-secondary-text">
                                {user.email}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="text-sm text-foreground">
                              {getRoleText(user.role)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span
                              className={`text-sm ${getStatusColor(
                                user.status
                              )}`}
                            >
                              {getStatusText(user.status)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-text">
                            {formatDate(user.created_at)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-text">
                            {user.last_login
                              ? formatDate(user.last_login)
                              : "从未登录"}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() =>
                                handleResetPassword(user.id, user.username)
                              }
                            >
                              重置密码
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() =>
                                handleUpdateUser(user.id, {
                                  status:
                                    user.status === "active"
                                      ? "suspended"
                                      : "active",
                                })
                              }
                            >
                              {user.status === "active" ? "冻结" : "解冻"}
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() =>
                                handleDeleteUser(user.id, user.username)
                              }
                              className="text-red-600 hover:text-red-700 border-red-300 hover:border-red-400"
                            >
                              删除
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* 分页 */}
                {totalPages > 1 && (
                  <div className="px-6 py-4 border-t border-border-color">
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-secondary-text">
                        第 {currentPage} 页，共 {totalPages} 页
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          disabled={currentPage === 1}
                          onClick={() => setCurrentPage(currentPage - 1)}
                        >
                          上一页
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          disabled={currentPage === totalPages}
                          onClick={() => setCurrentPage(currentPage + 1)}
                        >
                          下一页
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* 创建用户模态框 */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-card-background rounded-lg shadow-xl p-6 w-full max-w-md border border-border-color">
              <h3 className="text-lg font-semibold text-foreground mb-4">
                创建新用户
              </h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    用户名
                  </label>
                  <Input
                    type="text"
                    value={newUserData.username}
                    onChange={(e) =>
                      setNewUserData((prev) => ({
                        ...prev,
                        username: e.target.value,
                      }))
                    }
                    placeholder="请输入用户名"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    邮箱
                  </label>
                  <Input
                    type="email"
                    value={newUserData.email}
                    onChange={(e) =>
                      setNewUserData((prev) => ({
                        ...prev,
                        email: e.target.value,
                      }))
                    }
                    placeholder="请输入邮箱"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    密码
                  </label>
                  <Input
                    type="password"
                    value={newUserData.password}
                    onChange={(e) =>
                      setNewUserData((prev) => ({
                        ...prev,
                        password: e.target.value,
                      }))
                    }
                    placeholder="请输入密码"
                  />
                </div>

                <div>
                  <label
                    htmlFor="new-user-role"
                    className="block text-sm font-medium text-foreground mb-2"
                  >
                    角色
                  </label>
                  <select
                    id="new-user-role"
                    value={newUserData.role}
                    onChange={(e) =>
                      setNewUserData((prev) => ({
                        ...prev,
                        role: e.target.value,
                      }))
                    }
                    className="w-full px-4 py-2 rounded-md border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-foreground bg-white dark:bg-[#2c2c34] dark:border-[#4b4d61]"
                  >
                    <option value="user">普通用户</option>
                    <option value="moderator">版主</option>
                    <option value="admin">管理员</option>
                  </select>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowCreateModal(false);
                    setNewUserData({
                      username: "",
                      email: "",
                      password: "",
                      role: "user",
                    });
                  }}
                >
                  取消
                </Button>
                <Button onClick={handleCreateUser}>创建</Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminGuard>
  );
}

"use client";
import {
  checkResourceStatus,
  getShareLink,
  getValidResources,
  ApiResource,
} from "@/services/resourceService";
import { ResourceDetail } from "@/types/resource";
import React, {
  useEffect,
  useState,
  useCallback,
  useMemo,
  useRef,
} from "react";
import { useRouter } from "next/navigation";
import SearchBar from "@/components/SearchBar";
import { formatDate } from "@/lib/utils";
import Link from "next/link";
import Image from "next/image";
import {
  DocumentDuplicateIcon,
  ArrowTopRightOnSquareIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from "@heroicons/react/24/outline";
import { ResourceStructuredData } from "@/components/ResourceStructuredData";
import InvalidResourceFeedback from "@/components/InvalidResourceFeedback";
import { useResourceStatusCache } from "@/hooks/useResourceCache";
import { useResourceDetailPerformance } from "@/hooks/usePerformanceMonitor";
import { useToast } from "@/components/ToastProvider";
import PublisherInfo from "@/components/PublisherInfo";
import { useSearch } from "@/hooks/useSearch";
import { SEARCH_CONFIG, LINK_CONFIG } from "@/config/constants";

const getPanTypeName = (panType: number): string => {
  switch (panType) {
    case 1:
      return "百度网盘";
    case 2:
      return "夸克网盘";
    case 3:
      return "阿里云盘";
    case 4:
      return "迅雷网盘";
    default:
      return "其他网盘";
  }
};

const getPanTypeIcon = (panType: number): string | null => {
  switch (panType) {
    case 1:
      return "/images/baidupan.png";
    case 2:
      return "/images/quark.png";
    case 3:
      return "/images/alipan.png";
    case 4:
      return "/images/xunlei.png";
    default:
      return null;
  }
};

const getFileTypeName = (fileType?: string): string => {
  if (!fileType) return "其他";

  switch (fileType.toLowerCase()) {
    case "video":
      return "视频";
    case "audio":
      return "音频";
    case "image":
      return "图片";
    case "document":
      return "文档";
    case "archive":
      return "压缩包";
    case "application":
      return "应用";
    default:
      return "其他";
  }
};

const getPanTypePlatform = (panType: number): string => {
  switch (panType) {
    case 1:
      return "baidu";
    case 2:
      return "quark";
    case 3:
      return "aliyun";
    case 4:
      return "thunder";
    default:
      return "other";
  }
};

interface ResourceDetailClientProps {
  resource: ResourceDetail;
}

export default function ResourceDetailClient({
  resource,
}: ResourceDetailClientProps) {
  const router = useRouter();
  const { showToast } = useToast();

  const { searchType } = useSearch({
    initialType: SEARCH_CONFIG.defaultType,
  });

  // 使用缓存和性能监控
  const statusCache = useResourceStatusCache();
  const { measureApiCall } = useResourceDetailPerformance();

  // 使用 useRef 来存储函数引用，避免依赖问题
  const statusCacheRef = useRef(statusCache);
  const measureApiCallRef = useRef(measureApiCall);
  const showToastRef = useRef(showToast);

  // 更新 ref 的当前值
  statusCacheRef.current = statusCache;
  measureApiCallRef.current = measureApiCall;
  showToastRef.current = showToast;

  const [currentUrl, setCurrentUrl] = useState("");
  const [linkStatus, setLinkStatus] = useState({
    valid: false,
    message: "",
    checking: true,
  });
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [isLinkLoading, setIsLinkLoading] = useState(false);

  // 异步数据状态
  const [similarResources, setSimilarResources] = useState<ApiResource[]>([]);
  const [userResources, setUserResources] = useState<ApiResource[]>([]);
  const [isLoadingSimilar, setIsLoadingSimilar] = useState(true);
  const [isLoadingUser, setIsLoadingUser] = useState(true);

  // 异步加载相似资源
  const loadSimilarResources = useCallback(async () => {
    try {
      setIsLoadingSimilar(true);
      const similarData = await measureApiCallRef.current(
        "load_similar_resources",
        () => getValidResources(resource.title, resource.pan_type, 10, 1),
        {
          resourceKey: resource.resource_key,
          context: "similar_resources",
        }
      );

      // 过滤掉当前资源
      const filteredSimilar = similarData.filter(
        (item) => item.resource_id !== resource.resource_key
      );
      setSimilarResources(filteredSimilar);
    } catch (error) {
      console.error("Failed to load similar resources:", error);
      setSimilarResources([]);
    } finally {
      setIsLoadingSimilar(false);
    }
  }, [resource.title, resource.pan_type, resource.resource_key]);

  // 异步加载用户资源
  const loadUserResources = useCallback(async () => {
    try {
      setIsLoadingUser(true);
      const userData = await measureApiCallRef.current(
        "load_user_resources",
        () =>
          getValidResources(
            "",
            null,
            10,
            1,
            resource.author || "",
            "created_at"
          ),
        {
          resourceKey: resource.resource_key,
          context: "user_resources",
        }
      );

      // 过滤掉当前资源
      const filteredUser = userData.filter(
        (item) => item.resource_id !== resource.resource_key
      );
      setUserResources(filteredUser);
    } catch (error) {
      console.error("Failed to load user resources:", error);
      setUserResources([]);
    } finally {
      setIsLoadingUser(false);
    }
  }, [resource.author, resource.resource_key]);

  // 使用useMemo优化计算，避免不必要的重新计算
  const memoizedSimilarResources = useMemo(
    () => similarResources,
    [similarResources]
  );

  const memoizedUserResources = useMemo(() => userResources, [userResources]);

  const handleSearch = useCallback(
    (query: string, type: "local" | "online") => {
      if (query.trim()) {
        router.push(`/search?q=${encodeURIComponent(query)}&type=${type}`);
      }
    },
    [router]
  );

  const copyToClipboard = useCallback(
    (text: string, displayMessage: string) => {
      navigator.clipboard
        .writeText(text)
        .then(() => {
          showToast(displayMessage, "success");
        })
        .catch((err) => {
          console.error("无法复制: ", err);
          showToast("复制失败", "error");
        });
    },
    [showToast]
  );

  const handleLinkAction = useCallback(
    async (action: "copy" | "open") => {
      if (!resource || !linkStatus.valid) return;

      setIsLinkLoading(true);

      try {
        const platform = getPanTypePlatform(resource.pan_type);

        // 检查是否启用 get_share 接口
        const shouldUseGetShare =
          LINK_CONFIG.enableGetShareAPI &&
          LINK_CONFIG.enableGetShareForTypes.includes(platform);

        let shareUrl: string;

        if (!shouldUseGetShare) {
          // 仅调用 check_resource_status 接口
          const checkResponse = await checkResourceStatus(
            resource.resource_key,
            resource.pan_type
          );

          if (!checkResponse.valid) {
            throw new Error(checkResponse.message || "资源不可用");
          }

          // 从 checkResourceStatus 的响应中获取 share_url
          // 注意：这里需要后端 check_resource_status 接口返回 share_url
          const checkUrl = `/api/check_resource_status?resource_id=${resource.resource_key}&pan_type=${resource.pan_type}`;
          const checkRes = await fetch(checkUrl);
          const checkData = await checkRes.json();

          if (!checkData.valid || !checkData.share_url) {
            throw new Error(checkData.message || "资源不可用");
          }

          shareUrl = checkData.share_url;
        } else {
          // 调用 get_share 接口（原有逻辑）
          const response = await getShareLink(
            platform,
            resource.share_pwd,
            resource.resource_key
          );

          if (response.status === "success" && response.share_url) {
            shareUrl = response.share_url;
          } else {
            throw new Error(response.message || "获取链接失败");
          }
        }

        if (action === "copy") {
          // 复制按钮只复制链接，不包含提取码
          copyToClipboard(shareUrl, "链接已复制到粘贴板");
        } else {
          window.open(shareUrl, "_blank", "noopener,noreferrer");
        }
      } catch (error: any) {
        showToast(error.message || "操作失败，请稍后重试", "error");
      } finally {
        setIsLinkLoading(false);
      }
    },
    [resource, copyToClipboard, showToast, linkStatus.valid]
  );

  useEffect(() => {
    setCurrentUrl(window.location.href);

    const checkStatus = async () => {
      setLinkStatus((prev) => ({ ...prev, checking: true }));

      // 使用缓存和性能监控
      const cacheKey = `${resource.resource_key}_${resource.pan_type}`;
      let status = statusCacheRef.current.get(cacheKey);

      if (!status) {
        status = await measureApiCallRef.current(
          "initial_check_resource_status",
          () => checkResourceStatus(resource.resource_key, resource.pan_type),
          {
            resourceKey: resource.resource_key,
            panType: resource.pan_type,
            context: "initial_load",
          }
        );

        // 缓存有效的状态结果
        if (status.valid) {
          statusCacheRef.current.set(cacheKey, status);
        }
      }

      setLinkStatus({ ...status, checking: false });

      if (!status.valid) {
        showToastRef.current(
          "检测到资源检测已失效,已自动删除。",
          "error",
          4000
        );
      }
    };

    if (resource) {
      checkStatus();
    }
  }, [resource]); // 只依赖 resource，避免函数引用导致的无限循环

  // 异步加载相似资源和用户资源
  useEffect(() => {
    if (resource) {
      // 延迟加载相似资源，避免阻塞主要内容
      setTimeout(() => {
        loadSimilarResources();
      }, 100);

      // 延迟加载用户资源，进一步优化性能
      setTimeout(() => {
        loadUserResources();
      }, 200);
    }
  }, [resource, loadSimilarResources, loadUserResources]);

  return (
    <>
      {showFeedbackModal && (
        <InvalidResourceFeedback
          isOpen={showFeedbackModal}
          resourceId={resource.resource_key}
          resourceName={resource.title}
          panType={resource.pan_type}
          onClose={() => setShowFeedbackModal(false)}
          onFeedbackResult={(status, message) => {
            showToast(message, status === "success" ? "success" : "error");
          }}
        />
      )}
      <div className="min-h-screen py-4 sm:py-6 lg:py-8">
        {resource && currentUrl && (
          <ResourceStructuredData resource={resource} currentUrl={currentUrl} />
        )}
        <header className="container mx-auto">
          <div className="px-4 py-4">
            <SearchBar
              onSearch={handleSearch}
              placeholder="换个关键词试试"
              initialSearchType={searchType}
            />
          </div>
        </header>
        <main className="container mx-auto p-4 mt-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column */}
            <div className="lg:col-span-2 space-y-6">
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
                <h1 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
                  {resource.title}
                </h1>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-3 text-sm text-gray-600 dark:text-gray-400">
                  <div className="col-span-full">
                    <span className="font-semibold">资源预览:</span>{" "}
                    <p className="mt-1 dark:text-gray-300 whitespace-pre-line">
                      {resource.text_content}
                    </p>
                  </div>
                  <div>
                    <span className="font-semibold">分享时间:</span>{" "}
                    <span className="dark:text-white">
                      {formatDate(resource.updated_at)}
                    </span>
                  </div>
                  <div>
                    <span className="font-semibold">入库时间:</span>{" "}
                    <span className="dark:text-white">
                      {formatDate(resource.created_at)}
                    </span>
                  </div>
                  <div>
                    <span className="font-semibold">状态检测:</span>
                    {linkStatus.checking ? (
                      <span className="text-yellow-500 font-semibold animate-pulse ml-1">
                        检测中...
                      </span>
                    ) : linkStatus.valid ? (
                      <span className="text-green-500 font-semibold ml-1 inline-flex items-center">
                        <CheckCircleIcon className="w-4 h-4 mr-1" />
                        有效
                      </span>
                    ) : (
                      <span className="text-red-500 font-semibold ml-1 inline-flex items-center">
                        <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
                        {linkStatus.message || "失效"}
                      </span>
                    )}
                  </div>
                  <div>
                    <span className="font-semibold">文件类型:</span>{" "}
                    <span className="dark:text-white">
                      {getFileTypeName(resource.file_type)}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-semibold">网盘类型:</span>
                    <span className="dark:text-white inline-flex items-center gap-1">
                      {getPanTypeIcon(resource.pan_type) && (
                        <Image
                          src={getPanTypeIcon(resource.pan_type)!}
                          alt={`${getPanTypeName(resource.pan_type)}图标`}
                          width={16}
                          height={16}
                          className="flex-shrink-0"
                        />
                      )}
                      {getPanTypeName(resource.pan_type)}
                    </span>
                  </div>
                  <div>
                    <span className="font-semibold">资源有问题❓:</span>{" "}
                    <button
                      type="button"
                      onClick={() => setShowFeedbackModal(true)}
                      className="text-[var(--link-color)] hover:underline hover:text-[var(--button-hover)]"
                    >
                      点此反馈
                    </button>
                  </div>
                </div>
                <div className="mt-6 flex flex-wrap items-center gap-4">
                  <button
                    type="button"
                    onClick={() => handleLinkAction("open")}
                    disabled={isLinkLoading}
                    className="flex items-center justify-center gap-2 bg-[var(--button-background)] hover:bg-[var(--button-hover)] text-white px-4 py-2 rounded-sm transition-colors disabled:opacity-70 disabled:cursor-not-allowed"
                  >
                    {isLinkLoading ? (
                      <>
                        <div className="h-5 w-5 border-t-2 border-white rounded-full animate-spin"></div>
                        <span>资源校验中</span>
                      </>
                    ) : (
                      <>
                        <ArrowTopRightOnSquareIcon className="w-5 h-5" />
                        <span>进入网盘</span>
                      </>
                    )}
                  </button>
                  <button
                    type="button"
                    onClick={() => handleLinkAction("copy")}
                    disabled={isLinkLoading}
                    className="flex items-center justify-center gap-2 bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 px-4 py-2 rounded-sm transition-colors disabled:opacity-70 disabled:cursor-not-allowed dark-text-white"
                  >
                    {isLinkLoading ? (
                      <>
                        <div className="h-5 w-5 border-t-2 border-gray-500 rounded-full animate-spin"></div>
                        <span>资源校验中</span>
                      </>
                    ) : (
                      <>
                        <DocumentDuplicateIcon className="w-5 h-5" />
                        <span>复制链接</span>
                      </>
                    )}
                  </button>
                </div>
              </div>

              <div
                className="bg-[var(--card-background)] border-l-4 border-[var(--border-color)] text-[var(--secondary-text)] p-4 rounded-r-lg"
                role="alert"
              >
                <p>
                  本站数据均来自于互联网，如果你发现某个资源存在违规，可发送邮件到客服邮箱:{" "}
                  <a
                    href="mailto:<EMAIL>"
                    className="text-[var(--link-color)] hover:underline hover:text-[var(--button-hover)]"
                  >
                    <EMAIL>
                  </a>{" "}
                  提交举报信息
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
                <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
                  相似推荐
                </h2>
                {isLoadingSimilar ? (
                  <div className="flex items-center justify-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                    <span className="ml-2 text-gray-500 dark:text-gray-400">
                      加载中...
                    </span>
                  </div>
                ) : memoizedSimilarResources.length > 0 ? (
                  <ul className="space-y-2">
                    {memoizedSimilarResources.map((item) => (
                      <li key={item.resource_id}>
                        <Link
                          href={`/resources/${item.resource_id}`}
                          className="text-blue-500 hover:underline"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          {item.title}
                        </Link>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-gray-500 dark:text-gray-400">
                    暂无相似推荐
                  </p>
                )}
              </div>
            </div>

            {/* Right Column */}
            <div className="lg:col-span-1 space-y-6">
              <PublisherInfo
                author={resource.author || "97_bot"}
                authorAvatar={resource.author_avatar}
              />

              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
                <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
                  用户最近更新
                </h2>
                {isLoadingUser ? (
                  <div className="flex items-center justify-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                    <span className="ml-2 text-gray-500 dark:text-gray-400">
                      加载中...
                    </span>
                  </div>
                ) : memoizedUserResources.length > 0 ? (
                  <ul className="space-y-2">
                    {memoizedUserResources.map((item) => (
                      <li key={item.resource_id}>
                        <Link
                          href={`/resources/${item.resource_id}`}
                          className="text-blue-500 hover:underline"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          {item.title}
                        </Link>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-gray-500 dark:text-gray-400">
                    暂无该用户的其它资源
                  </p>
                )}
              </div>
            </div>
          </div>
        </main>
      </div>
    </>
  );
}

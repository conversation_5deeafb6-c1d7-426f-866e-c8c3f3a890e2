import { checkResourceStatus, getShareLink } from "@/services/resourceService";
import { LINK_CONFIG } from "@/config/constants";

type CloudType = "baidu" | "quark" | "thunder" | "aliyun";

interface ProcessCloudLinkParams {
  type: CloudType;
  idToUse: string;
  searchType?: "local" | "online";
  baiduLink?: string;
  quarkLink?: string;
  aliyunLink?: string;
  thunderLink?: string;
}

interface ProcessCloudLinkResult {
  success: boolean;
  url?: string;
  error?: string;
}

/**
 * 打开链接的通用逻辑
 */
export const openLinkInNewTab = (url: string) => {
  const link = document.createElement("a");
  link.href = url;
  link.target = "_blank";
  link.rel = "noreferrer noopener nofollow";
  link.referrerPolicy = "no-referrer";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

/**
 * 处理云盘链接的核心逻辑
 */
export const processCloudLink = async ({
  type,
  idToUse,
  searchType = "local",
}: ProcessCloudLinkParams): Promise<ProcessCloudLinkResult> => {
  const panTypeMap: Record<string, number> = {
    baidu: 1,
    quark: 2,
    thunder: 4,
    aliyun: 3,
  };

  try {
    // 检查是否启用 get_share 接口
    const shouldUseGetShare =
      LINK_CONFIG.enableGetShareAPI &&
      LINK_CONFIG.enableGetShareForTypes.includes(type);

    // 如果禁用 get_share 接口或者当前类型不在启用列表中，只调用 check_resource_status
    if (!shouldUseGetShare) {
      const checkUrl = `/api/check_resource_status?resource_id=${idToUse}&pan_type=${panTypeMap[type]}`;
      const checkRes = await fetch(checkUrl);
      const checkData = await checkRes.json();

      if (!checkData.valid || !checkData.share_url) {
        return {
          success: false,
          error: checkData.message || "资源不可用",
        };
      }

      return {
        success: true,
        url: checkData.share_url,
      };
    }

    // 原有逻辑：启用 get_share 接口的情况
    // 迅雷和阿里云只调用check_resource_status（保持原有逻辑）
    if (type === "thunder" || type === "aliyun") {
      const checkUrl = `/api/check_resource_status?resource_id=${idToUse}&pan_type=${panTypeMap[type]}`;
      const checkRes = await fetch(checkUrl);
      const checkData = await checkRes.json();

      if (!checkData.valid || !checkData.share_url) {
        return {
          success: false,
          error: checkData.message || "资源不可用",
        };
      }

      return {
        success: true,
        url: checkData.share_url,
      };
    }

    // 百度和夸克的处理逻辑（启用 get_share 时）
    if (searchType === "online") {
      // 1. 校验资源有效性
      const status = await checkResourceStatus(idToUse, panTypeMap[type]);
      if (!status.valid) {
        return {
          success: false,
          error: status.message || "资源不可用",
        };
      }
    }

    // 2. 获取分享链接
    const shareLinkResponse = await getShareLink(type, undefined, idToUse);
    if (shareLinkResponse.status === "success" && shareLinkResponse.share_url) {
      return {
        success: true,
        url: shareLinkResponse.share_url,
      };
    } else {
      return {
        success: false,
        error: shareLinkResponse.message || "获取分享链接失败",
      };
    }
  } catch {
    return {
      success: false,
      error: "处理链接出错",
    };
  }
};

/**
 * 获取原始链接
 */
export const getOriginalLink = (
  type: CloudType,
  baiduLink?: string,
  quarkLink?: string,
  aliyunLink?: string,
  thunderLink?: string
): string => {
  switch (type) {
    case "baidu":
      return baiduLink || "";
    case "quark":
      return quarkLink || "";
    case "thunder":
      return thunderLink || "";
    case "aliyun":
      return aliyunLink || "";
    default:
      return "";
  }
};

"use client";

import { useState } from "react";
import PageHeader from "@/components/PageHeader";
import { useToast } from "@/components/ToastProvider";

interface SubmissionStatusResult {
  task_id: string;
  original_url: string;
  status: string;
  resource_title: string | null;
  error_message: string | null;
  updated_at: string;
}

const statusMapping: { [key: string]: string } = {
  pending: "待处理",
  processing: "处理中",
  success: "已处理完成",
  failed_parse_url: "URL解析失败",
  failed_fetch_details: "获取详情失败",
  duplicate_parsed: "重复资源且已解析",
  duplicate_pending_parse: "重复资源且待解析/正在解析",
  invalid_url: "无效的URL格式或不支持的网盘",
  accepted: "已接受",
  failed_duplicate: "重复资源",
};

const getStatusColorClass = (status: string): string => {
  const s = status.toLowerCase();
  switch (s) {
    case "success":
      return "text-green-600 dark:text-green-400";
    case "failed_parse_url":
    case "failed_fetch_details":
    case "invalid_url":
      return "text-red-600 dark:text-red-400";
    case "processing":
    case "accepted":
    case "failed_duplicate":
    case "duplicate_parsed":
    case "duplicate_pending_parse":
      return "text-yellow-600 dark:text-yellow-400";
    default:
      return "text-[var(--secondary-text)]";
  }
};

export default function SubmitPage() {
  const [resources, setResources] = useState("");
  const [queryLinks, setQueryLinks] = useState("");
  const [isSubmittingResources, setIsSubmittingResources] = useState(false);
  const [isQueryingStatus, setIsQueryingStatus] = useState(false);
  const [queryResult, setQueryResult] = useState<
    SubmissionStatusResult[] | null
  >(null);
  const [submissionResult, setSubmissionResult] = useState<any>(null);
  const { showToast } = useToast();

  const resourceLines = resources.split("\n").filter(Boolean);
  const resourceCount = resourceLines.length;

  // 提交资源
  const handleSubmitResources = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!resources.trim()) return;

    // 检查链接是否包含中文
    const containsChinese = (text: string) => /[\u4e00-\u9fa5]/.test(text);
    const urls = resources
      .split("\n")
      .map((url) => url.trim())
      .filter(Boolean);

    if (urls.length > 500) {
      showToast("单次最多提交100条链接。", "error");
      return;
    }

    if (urls.some(containsChinese)) {
      showToast("提交错误，请单独提交链接，不要包含中文。", "error");
      return;
    }

    setIsSubmittingResources(true);
    setSubmissionResult(null);
    try {
      const response = await fetch("/api/submit_resources", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          urls: urls.map((url) => ({ url })),
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "提交失败");
      }

      setResources("");
      showToast(data.message || "资源提交成功！", "success");
      setSubmissionResult(data);
    } catch (error: any) {
      console.error("提交资源失败:", error);
      showToast(error.message || "提交失败，请稍后重试", "error");
    } finally {
      setIsSubmittingResources(false);
    }
  };

  // 查询链接状态
  const handleQueryLinks = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!queryLinks.trim()) return;

    setIsQueryingStatus(true);
    setQueryResult(null);
    try {
      const urls = queryLinks
        .split("\n")
        .map((url) => url.trim())
        .filter(Boolean);
      const response = await fetch("/api/query_submission_status", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ urls }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "查询失败");
      }

      if (data && data.length === 0) {
        showToast("未查询到链接状态信息", "error");
      }
      setQueryResult(data);
    } catch (error: any) {
      console.error("查询链接失败:", error);
      showToast(error.message || "查询失败，请稍后重试", "error");
    } finally {
      setIsQueryingStatus(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <PageHeader
        title="资源提交"
        description="提交网盘资源链接，帮助更多人找到资源"
      />

      <div className="mt-8 space-y-8">
        {/* 资源提交表单 */}
        <div className="bg-[var(--card-background)] p-6 rounded-xl shadow-sm border border-[var(--border-color)]">
          <h2 className="text-xl font-semibold mb-4">提交资源</h2>
          <form onSubmit={handleSubmitResources}>
            <div className="mb-4">
              <textarea
                value={resources}
                onChange={(e) => {
                  const inputValue = e.target.value;
                  const lines = inputValue.split("\n");
                  const extractedLinks = lines.map((line) => {
                    const match = line.match(/(https?:\/\/\S+)/);
                    return match ? match[0] : line;
                  });
                  const newValue = extractedLinks.join("\n");
                  setResources(newValue);

                  if (submissionResult) {
                    setSubmissionResult(null);
                  }
                }}
                placeholder={`支持网盘类型:百度网盘、阿里云盘、夸克网盘、迅雷云盘
单次最多支持提交500条,一行一条链接
请勿提交违规内容、纯引流内容,严重违规者封禁提交权限`}
                className="w-full h-40 p-4 rounded-lg border border-[var(--border-color)] bg-[var(--background)] text-[var(--foreground)] focus:outline-none focus:ring-2 focus:ring-[var(--button-background)]"
                required
              />
              <p
                className={`text-right text-sm mt-1 ${
                  resourceCount > 500
                    ? "text-red-500"
                    : "text-[var(--secondary-text)]"
                }`}
              >
                {resourceCount} / 500
              </p>
            </div>
            <button
              type="submit"
              disabled={isSubmittingResources}
              className="w-full py-3 bg-[var(--button-background)] hover:bg-[var(--button-hover)] text-white font-medium rounded-lg transition-colors duration-200 disabled:opacity-50"
            >
              {isSubmittingResources ? "提交中..." : "提交资源"}
            </button>
          </form>

          {submissionResult && (
            <div className="mt-6 border-t border-[var(--border-color)] pt-4">
              <h3 className="text-lg font-medium mb-3">提交结果</h3>
              <div className="space-y-2">
                <p className="font-semibold">{submissionResult.message}</p>
                <p className="text-sm text-[var(--secondary-text)]">
                  总共提交: {submissionResult.total_submitted}, 成功处理:{" "}
                  {submissionResult.accepted_for_processing}
                </p>
              </div>

              {submissionResult.initial_results?.length > 0 && (
                <div className="mt-4 overflow-x-auto">
                  <table className="min-w-full">
                    <thead>
                      <tr className="bg-[var(--hover-background)] text-left">
                        <th className="py-2 px-4 rounded-tl-lg">链接</th>
                        <th className="py-2 px-4">状态</th>
                        <th className="py-2 px-4 rounded-tr-lg">信息</th>
                      </tr>
                    </thead>
                    <tbody>
                      {submissionResult.initial_results.map(
                        (result: any, index: number) => (
                          <tr
                            key={index}
                            className={
                              index % 2 === 0
                                ? "bg-[var(--background)]"
                                : "bg-[var(--card-background)]"
                            }
                          >
                            <td className="py-2 px-4 truncate max-w-xs">
                              {result.url}
                            </td>
                            <td
                              className={`py-2 px-4 font-medium ${
                                result.status === "accepted"
                                  ? "text-green-600 dark:text-green-400"
                                  : "text-red-600 dark:text-red-400"
                              }`}
                            >
                              {result.status === "FAILED_DUPLICATE"
                                ? "资源已重复"
                                : statusMapping[result.status.toLowerCase()] ||
                                  result.status}
                            </td>
                            <td className="py-2 px-4 text-[var(--secondary-text)]">
                              {result.status === "FAILED_DUPLICATE"
                                ? result.notes
                                : result.message}
                            </td>
                          </tr>
                        )
                      )}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}
        </div>

        {/* 链接状态查询表单 */}
        <div className="bg-[var(--card-background)] p-6 rounded-xl shadow-sm border border-[var(--border-color)]">
          <h2 className="text-xl font-semibold mb-4">查询链接状态</h2>
          <form onSubmit={handleQueryLinks}>
            <div className="mb-4">
              <textarea
                value={queryLinks}
                onChange={(e) => {
                  setQueryLinks(e.target.value);
                  if (queryResult) {
                    setQueryResult(null);
                  }
                }}
                placeholder="输入需要查询的链接，支持多条链接查询，一行一条"
                className="w-full h-32 p-4 rounded-lg border border-[var(--border-color)] bg-[var(--background)] text-[var(--foreground)] focus:outline-none focus:ring-2 focus:ring-[var(--button-background)]"
                required
              />
            </div>
            <button
              type="submit"
              disabled={isQueryingStatus}
              className="w-full py-3 bg-[var(--button-background)] hover:bg-[var(--button-hover)] text-white font-medium rounded-lg transition-colors duration-200 disabled:opacity-50"
            >
              {isQueryingStatus ? "查询中..." : "查询链接状态"}
            </button>
          </form>

          {/* 查询结果显示 */}
          {queryResult && queryResult.length > 0 && (
            <div className="mt-6 border-t border-[var(--border-color)] pt-4">
              <h3 className="text-lg font-medium mb-3">查询结果</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full">
                  <thead>
                    <tr className="bg-[var(--hover-background)] text-left">
                      <th className="py-2 px-4 rounded-tl-lg">链接</th>
                      <th className="py-2 px-4">状态</th>
                      <th className="py-2 px-4">详情</th>
                      <th className="py-2 px-4 rounded-tr-lg">最后更新时间</th>
                    </tr>
                  </thead>
                  <tbody>
                    {queryResult.map((result, index) => (
                      <tr
                        key={result.task_id}
                        className={
                          index % 2 === 0
                            ? "bg-[var(--background)]"
                            : "bg-[var(--card-background)]"
                        }
                      >
                        <td className="py-2 px-4 truncate max-w-xs">
                          <a
                            href={result.original_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-500 hover:underline"
                          >
                            {result.original_url}
                          </a>
                        </td>
                        <td className="py-2 px-4">
                          <span className={getStatusColorClass(result.status)}>
                            {statusMapping[result.status.toLowerCase()] ||
                              result.status}
                          </span>
                        </td>
                        <td className="py-2 px-4">
                          {result.resource_title ||
                            result.error_message ||
                            "N/A"}
                        </td>
                        <td className="py-2 px-4">
                          {new Date(result.updated_at).toLocaleString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// "use client";

// export default function SubmitPage() {
//   return (
//     <div className="flex items-center justify-center h-[calc(100vh-200px)]">
//       <div className="text-center">
//         <h1 className="text-6xl font-bold text-[var(--foreground)]">维护中</h1>
//         <p className="mt-4 text-xl text-[var(--secondary-text)]">
//           资源提交功能正在升级改造中，暂停使用，敬请谅解。
//         </p>
//       </div>
//     </div>
//   );
// }

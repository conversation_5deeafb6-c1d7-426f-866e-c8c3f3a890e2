/**
 * 性能测试工具
 * 用于测试和验证性能优化效果
 */

interface PerformanceTestResult {
  testName: string;
  duration: number;
  success: boolean;
  error?: string;
  metadata?: Record<string, any>;
}

interface PerformanceTestSuite {
  name: string;
  tests: PerformanceTestResult[];
  totalDuration: number;
  successRate: number;
}

class PerformanceTest {
  private results: PerformanceTestResult[] = [];

  /**
   * 测试页面加载性能
   */
  async testPageLoad(url: string): Promise<PerformanceTestResult> {
    const startTime = performance.now();
    
    try {
      // 模拟页面加载
      await new Promise(resolve => {
        const img = new Image();
        img.onload = resolve;
        img.onerror = resolve;
        img.src = url + '?t=' + Date.now();
      });
      
      const duration = performance.now() - startTime;
      
      return {
        testName: 'Page Load',
        duration,
        success: true,
        metadata: { url }
      };
    } catch (error) {
      return {
        testName: 'Page Load',
        duration: performance.now() - startTime,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * 测试API调用性能
   */
  async testApiCall(apiCall: () => Promise<any>, testName: string): Promise<PerformanceTestResult> {
    const startTime = performance.now();
    
    try {
      await apiCall();
      const duration = performance.now() - startTime;
      
      return {
        testName: `API Call - ${testName}`,
        duration,
        success: true
      };
    } catch (error) {
      return {
        testName: `API Call - ${testName}`,
        duration: performance.now() - startTime,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * 测试组件渲染性能
   */
  async testComponentRender(renderFn: () => void, testName: string): Promise<PerformanceTestResult> {
    const startTime = performance.now();
    
    try {
      renderFn();
      const duration = performance.now() - startTime;
      
      return {
        testName: `Component Render - ${testName}`,
        duration,
        success: true
      };
    } catch (error) {
      return {
        testName: `Component Render - ${testName}`,
        duration: performance.now() - startTime,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * 测试缓存性能
   */
  async testCachePerformance(
    cacheGet: (key: string) => any,
    cacheSet: (key: string, value: any) => void,
    testData: { key: string; value: any }[]
  ): Promise<PerformanceTestResult> {
    const startTime = performance.now();
    
    try {
      // 测试写入性能
      for (const { key, value } of testData) {
        cacheSet(key, value);
      }
      
      // 测试读取性能
      for (const { key } of testData) {
        cacheGet(key);
      }
      
      const duration = performance.now() - startTime;
      
      return {
        testName: 'Cache Performance',
        duration,
        success: true,
        metadata: { operations: testData.length * 2 }
      };
    } catch (error) {
      return {
        testName: 'Cache Performance',
        duration: performance.now() - startTime,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * 运行性能测试套件
   */
  async runTestSuite(suiteName: string, tests: Array<() => Promise<PerformanceTestResult>>): Promise<PerformanceTestSuite> {
    const suiteStartTime = performance.now();
    const results: PerformanceTestResult[] = [];
    
    for (const test of tests) {
      const result = await test();
      results.push(result);
      this.results.push(result);
    }
    
    const totalDuration = performance.now() - suiteStartTime;
    const successCount = results.filter(r => r.success).length;
    const successRate = (successCount / results.length) * 100;
    
    return {
      name: suiteName,
      tests: results,
      totalDuration,
      successRate
    };
  }

  /**
   * 生成性能报告
   */
  generateReport(): string {
    const totalTests = this.results.length;
    const successfulTests = this.results.filter(r => r.success).length;
    const averageDuration = this.results.reduce((sum, r) => sum + r.duration, 0) / totalTests;
    
    let report = `性能测试报告\n`;
    report += `================\n`;
    report += `总测试数: ${totalTests}\n`;
    report += `成功测试: ${successfulTests}\n`;
    report += `成功率: ${((successfulTests / totalTests) * 100).toFixed(2)}%\n`;
    report += `平均耗时: ${averageDuration.toFixed(2)}ms\n\n`;
    
    report += `详细结果:\n`;
    report += `----------\n`;
    
    this.results.forEach((result, index) => {
      report += `${index + 1}. ${result.testName}\n`;
      report += `   耗时: ${result.duration.toFixed(2)}ms\n`;
      report += `   状态: ${result.success ? '✅ 成功' : '❌ 失败'}\n`;
      if (result.error) {
        report += `   错误: ${result.error}\n`;
      }
      if (result.metadata) {
        report += `   元数据: ${JSON.stringify(result.metadata)}\n`;
      }
      report += `\n`;
    });
    
    return report;
  }

  /**
   * 清空测试结果
   */
  clearResults(): void {
    this.results = [];
  }

  /**
   * 获取性能基准
   */
  getPerformanceBenchmarks() {
    return {
      pageLoad: {
        excellent: 500,
        good: 1000,
        acceptable: 2000
      },
      apiCall: {
        excellent: 100,
        good: 300,
        acceptable: 1000
      },
      componentRender: {
        excellent: 16,
        good: 50,
        acceptable: 100
      }
    };
  }

  /**
   * 评估性能等级
   */
  evaluatePerformance(duration: number, type: 'pageLoad' | 'apiCall' | 'componentRender'): string {
    const benchmarks = this.getPerformanceBenchmarks()[type];
    
    if (duration <= benchmarks.excellent) return '优秀';
    if (duration <= benchmarks.good) return '良好';
    if (duration <= benchmarks.acceptable) return '可接受';
    return '需要优化';
  }
}

// 创建全局性能测试实例
export const globalPerformanceTest = new PerformanceTest();

export default PerformanceTest;

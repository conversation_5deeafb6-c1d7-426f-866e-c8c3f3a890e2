"use client";

import { useState } from "react";
import Link from "next/link";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import { useToast } from "@/components/ToastProvider";
import { forgotPassword, ForgotPasswordRequest } from "@/services/authService";
import PageHeader from "@/components/PageHeader";

export default function ForgotPasswordPage() {
  const { showToast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [formData, setFormData] = useState<ForgotPasswordRequest>({
    email: "",
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const validateForm = () => {
    if (!formData.email.trim()) {
      showToast("请输入邮箱地址", "error");
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      showToast("请输入有效的邮箱地址", "error");
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const result = await forgotPassword(formData);

      if (result.success) {
        showToast(result.message, "success");
        setEmailSent(true);
      } else {
        showToast(result.message, "error");
      }
    } catch (error) {
      console.error("忘记密码请求失败:", error);
      showToast("发送重置邮件失败，请稍后重试", "error");
    } finally {
      setIsLoading(false);
    }
  };

  const handleResend = () => {
    setEmailSent(false);
    setFormData({ email: "" });
  };

  return (
    <div className="min-h-screen bg-background">
      <PageHeader title="忘记密码" description="重置您的账户密码" />

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-md mx-auto">
          <div className="bg-card-background rounded-lg shadow-lg p-8 border border-border-color">
            {!emailSent ? (
              <>
                <div className="text-center mb-8">
                  <h1 className="text-2xl font-bold text-foreground mb-2">
                    忘记密码
                  </h1>
                  <p className="text-secondary-text">
                    请输入您的邮箱地址，我们将发送重置密码的链接
                  </p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <label
                      htmlFor="email"
                      className="block text-sm font-medium text-foreground mb-2"
                    >
                      邮箱地址
                    </label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="请输入您的邮箱地址"
                      required
                      className="w-full"
                    />
                  </div>

                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="w-full"
                    size="lg"
                  >
                    {isLoading ? "发送中..." : "发送重置邮件"}
                  </Button>
                </form>

                <div className="mt-8 text-center">
                  <Link
                    href="/login"
                    className="text-link-color hover:text-button-hover font-medium transition-colors"
                  >
                    ← 返回登录
                  </Link>
                </div>
              </>
            ) : (
              <>
                <div className="text-center mb-8">
                  <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg
                      className="w-8 h-8 text-green-600 dark:text-green-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>
                  <h1 className="text-2xl font-bold text-foreground mb-2">
                    邮件已发送
                  </h1>
                  <p className="text-secondary-text">
                    我们已向{" "}
                    <span className="font-medium text-foreground">
                      {formData.email}
                    </span>{" "}
                    发送了重置密码的邮件
                  </p>
                </div>

                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg
                        className="h-5 w-5 text-blue-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-blue-800 dark:text-blue-200">
                        请检查您的邮箱（包括垃圾邮件文件夹），点击邮件中的链接重置密码。
                        如果几分钟内没有收到邮件，请检查邮箱地址是否正确。
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <Button
                    onClick={handleResend}
                    variant="outline"
                    className="w-full"
                    size="lg"
                  >
                    重新发送邮件
                  </Button>

                  <div className="text-center">
                    <Link
                      href="/login"
                      className="text-link-color hover:text-button-hover font-medium transition-colors"
                    >
                      ← 返回登录
                    </Link>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

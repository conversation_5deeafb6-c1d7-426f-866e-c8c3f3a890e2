"use client";

import { useEffect, useRef, useCallback } from "react";

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

interface PerformanceMonitorConfig {
  enableLogging: boolean;
  enableAnalytics: boolean;
  sampleRate: number; // 0-1, 采样率
}

/**
 * 性能监控Hook
 * 用于测量页面加载时间、API调用时间等关键性能指标
 */
export const usePerformanceMonitor = (
  pageName: string,
  config: Partial<PerformanceMonitorConfig> = {}
) => {
  const defaultConfig: PerformanceMonitorConfig = {
    enableLogging: process.env.NODE_ENV === "development",
    enableAnalytics: process.env.NODE_ENV === "production",
    sampleRate: 0.1, // 10%采样率
  };

  const finalConfig = { ...defaultConfig, ...config };
  const metricsRef = useRef<PerformanceMetric[]>([]);
  const timersRef = useRef<Map<string, number>>(new Map());

  // 发送指标到分析服务
  const sendToAnalytics = useCallback(
    (metric: PerformanceMetric) => {
      if (!finalConfig.enableAnalytics) return;
      if (Math.random() > finalConfig.sampleRate) return;

      // 发送到Google Analytics或其他分析服务
      if (typeof window !== "undefined" && (window as any).gtag) {
        (window as any).gtag("event", "performance_metric", {
          metric_name: metric.name,
          metric_value: Math.round(metric.value),
          page_name: pageName,
          custom_parameter_1: metric.metadata?.type || "unknown",
        });
      }
    },
    [finalConfig.enableAnalytics, finalConfig.sampleRate, pageName]
  );

  // 记录指标
  const recordMetric = useCallback(
    (name: string, value: number, metadata?: Record<string, any>) => {
      const metric: PerformanceMetric = {
        name,
        value,
        timestamp: Date.now(),
        metadata,
      };

      metricsRef.current.push(metric);

      if (finalConfig.enableLogging) {
        console.log(
          `[Performance] ${pageName} - ${name}: ${value.toFixed(2)}ms`,
          metadata
        );
      }

      sendToAnalytics(metric);
    },
    [finalConfig.enableLogging, pageName, sendToAnalytics]
  );

  // 开始计时
  const startTimer = useCallback((timerName: string) => {
    timersRef.current.set(timerName, performance.now());
  }, []);

  // 结束计时并记录
  const endTimer = useCallback(
    (timerName: string, metadata?: Record<string, any>) => {
      const startTime = timersRef.current.get(timerName);
      if (startTime === undefined) {
        console.warn(`Timer "${timerName}" was not started`);
        return 0;
      }

      const duration = performance.now() - startTime;
      timersRef.current.delete(timerName);
      recordMetric(timerName, duration, metadata);
      return duration;
    },
    [recordMetric]
  );

  // 测量API调用时间
  const measureApiCall = useCallback(
    async <T>(
      apiName: string,
      apiCall: () => Promise<T>,
      metadata?: Record<string, any>
    ): Promise<T> => {
      const startTime = performance.now();
      try {
        const result = await apiCall();
        const duration = performance.now() - startTime;
        recordMetric(`api_${apiName}`, duration, {
          ...metadata,
          type: "api_call",
          status: "success",
        });
        return result;
      } catch (error) {
        const duration = performance.now() - startTime;
        recordMetric(`api_${apiName}`, duration, {
          ...metadata,
          type: "api_call",
          status: "error",
          error: error instanceof Error ? error.message : "unknown",
        });
        throw error;
      }
    },
    [recordMetric]
  );

  // 测量组件渲染时间
  const measureRender = useCallback(
    (componentName: string) => {
      const startTime = performance.now();

      return () => {
        const duration = performance.now() - startTime;
        recordMetric(`render_${componentName}`, duration, {
          type: "component_render",
        });
      };
    },
    [recordMetric]
  );

  // 获取Web Vitals指标
  const measureWebVitals = useCallback(() => {
    if (typeof window === "undefined") return;

    // 测量LCP (Largest Contentful Paint)
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      recordMetric("lcp", lastEntry.startTime, { type: "web_vital" });
    });

    try {
      observer.observe({ entryTypes: ["largest-contentful-paint"] });
    } catch {
      // LCP可能不被支持
    }

    // 测量FID (First Input Delay)
    const fidObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        recordMetric("fid", entry.processingStart - entry.startTime, {
          type: "web_vital",
        });
      });
    });

    try {
      fidObserver.observe({ entryTypes: ["first-input"] });
    } catch {
      // FID可能不被支持
    }

    // 清理函数
    return () => {
      observer.disconnect();
      fidObserver.disconnect();
    };
  }, [recordMetric]);

  // 获取所有指标
  const getMetrics = useCallback(() => {
    return [...metricsRef.current];
  }, []);

  // 获取指标摘要
  const getMetricsSummary = useCallback(() => {
    const metrics = metricsRef.current;
    const summary: Record<
      string,
      { count: number; avg: number; min: number; max: number }
    > = {};

    metrics.forEach((metric) => {
      if (!summary[metric.name]) {
        summary[metric.name] = {
          count: 0,
          avg: 0,
          min: Infinity,
          max: -Infinity,
        };
      }

      const s = summary[metric.name];
      s.count++;
      s.min = Math.min(s.min, metric.value);
      s.max = Math.max(s.max, metric.value);
    });

    // 计算平均值
    Object.keys(summary).forEach((name) => {
      const values = metrics.filter((m) => m.name === name).map((m) => m.value);
      summary[name].avg = values.reduce((a, b) => a + b, 0) / values.length;
    });

    return summary;
  }, []);

  // 页面加载时开始监控
  useEffect(() => {
    startTimer("page_load");
    const cleanup = measureWebVitals();

    return () => {
      endTimer("page_load");
      cleanup?.();
    };
  }, [startTimer, endTimer, measureWebVitals]);

  return {
    recordMetric,
    startTimer,
    endTimer,
    measureApiCall,
    measureRender,
    getMetrics,
    getMetricsSummary,
  };
};

/**
 * 专门用于资源详情页面的性能监控Hook
 */
export const useResourceDetailPerformance = () => {
  return usePerformanceMonitor("resource_detail", {
    enableLogging: true,
    sampleRate: 0.2, // 资源详情页面使用更高的采样率
  });
};

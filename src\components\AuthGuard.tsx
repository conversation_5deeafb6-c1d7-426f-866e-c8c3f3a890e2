"use client";

import { useEffect, ReactNode } from "react";
import { useAuth } from "@/hooks/useAuth";

interface AuthGuardProps {
  children: ReactNode;
  requireAuth?: boolean;
  requiredRole?: string;
  fallback?: ReactNode;
}

export default function AuthGuard({ 
  children, 
  requireAuth = false, 
  requiredRole,
  fallback 
}: AuthGuardProps) {
  const { isLoading, isAuthenticated, hasPermission, requireAuth: doRequireAuth, requirePermission } = useAuth();

  useEffect(() => {
    if (isLoading) return;

    if (requireAuth && !isAuthenticated) {
      doRequireAuth();
      return;
    }

    if (requiredRole && !hasPermission(requiredRole)) {
      requirePermission(requiredRole);
      return;
    }
  }, [isLoading, isAuthenticated, requireAuth, requiredRole, doRequireAuth, hasPermission, requirePermission]);

  // 显示加载状态
  if (isLoading) {
    return (
      fallback || (
        <div className="min-h-screen bg-background flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-button-background mx-auto mb-4"></div>
            <p className="text-secondary-text">验证身份中...</p>
          </div>
        </div>
      )
    );
  }

  // 检查认证要求
  if (requireAuth && !isAuthenticated) {
    return (
      fallback || (
        <div className="min-h-screen bg-background flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-foreground mb-2">需要登录</h2>
            <p className="text-secondary-text">正在跳转到登录页面...</p>
          </div>
        </div>
      )
    );
  }

  // 检查权限要求
  if (requiredRole && !hasPermission(requiredRole)) {
    return (
      fallback || (
        <div className="min-h-screen bg-background flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-foreground mb-2">权限不足</h2>
            <p className="text-secondary-text">您没有权限访问此页面</p>
          </div>
        </div>
      )
    );
  }

  return <>{children}</>;
}

// 便捷的高阶组件
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options: { requireAuth?: boolean; requiredRole?: string } = {}
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <AuthGuard requireAuth={options.requireAuth} requiredRole={options.requiredRole}>
        <Component {...props} />
      </AuthGuard>
    );
  };
}

// 管理员保护组件
export function AdminGuard({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <AuthGuard requireAuth={true} requiredRole="admin" fallback={fallback}>
      {children}
    </AuthGuard>
  );
}

// 版主保护组件
export function ModeratorGuard({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <AuthGuard requireAuth={true} requiredRole="moderator" fallback={fallback}>
      {children}
    </AuthGuard>
  );
}

"use client";

import { 
  getFeatureConfig, 
  isRegistrationEnabled, 
  shouldShowRegistrationInNav, 
  shouldShowRegistrationInLogin 
} from "@/config/features";
import PageHeader from "@/components/PageHeader";

export default function FeatureTestPage() {
  const config = getFeatureConfig();

  return (
    <div className="min-h-screen bg-background">
      <PageHeader 
        title="功能配置测试"
        description="查看当前功能配置状态"
      />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="bg-card-background rounded-lg shadow-lg p-8 border border-border-color">
            <h2 className="text-xl font-semibold text-foreground mb-6">当前功能配置</h2>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-hover-background rounded-lg">
                <span className="text-foreground font-medium">注册功能启用状态</span>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  config.enableRegistration 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                }`}>
                  {config.enableRegistration ? '已启用' : '已禁用'}
                </span>
              </div>

              <div className="flex items-center justify-between p-4 bg-hover-background rounded-lg">
                <span className="text-foreground font-medium">导航栏注册按钮</span>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  config.showRegistrationInNav 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                }`}>
                  {config.showRegistrationInNav ? '显示' : '隐藏'}
                </span>
              </div>

              <div className="flex items-center justify-between p-4 bg-hover-background rounded-lg">
                <span className="text-foreground font-medium">登录页面注册链接</span>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  config.showRegistrationInLogin 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                }`}>
                  {config.showRegistrationInLogin ? '显示' : '隐藏'}
                </span>
              </div>
            </div>

            <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <h3 className="text-lg font-medium text-blue-800 dark:text-blue-200 mb-2">
                环境变量配置
              </h3>
              <div className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                <p><code>NEXT_PUBLIC_ENABLE_REGISTRATION</code>: {process.env.NEXT_PUBLIC_ENABLE_REGISTRATION || '未设置'}</p>
                <p><code>NEXT_PUBLIC_SHOW_REGISTRATION_IN_NAV</code>: {process.env.NEXT_PUBLIC_SHOW_REGISTRATION_IN_NAV || '未设置'}</p>
                <p><code>NEXT_PUBLIC_SHOW_REGISTRATION_IN_LOGIN</code>: {process.env.NEXT_PUBLIC_SHOW_REGISTRATION_IN_LOGIN || '未设置'}</p>
              </div>
            </div>

            <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
              <h3 className="text-lg font-medium text-yellow-800 dark:text-yellow-200 mb-2">
                使用说明
              </h3>
              <div className="text-sm text-yellow-700 dark:text-yellow-300 space-y-2">
                <p>• 要启用注册功能，请在环境变量中设置 <code>NEXT_PUBLIC_ENABLE_REGISTRATION=true</code></p>
                <p>• 要在导航栏显示注册按钮，请设置 <code>NEXT_PUBLIC_SHOW_REGISTRATION_IN_NAV=true</code></p>
                <p>• 要在登录页面显示注册链接，请设置 <code>NEXT_PUBLIC_SHOW_REGISTRATION_IN_LOGIN=true</code></p>
                <p>• 也可以在登录页面URL中添加 <code>?showRegister=true</code> 参数来临时显示注册链接</p>
              </div>
            </div>

            <div className="mt-6 flex space-x-4">
              <a 
                href="/login" 
                className="px-4 py-2 bg-button-background text-white rounded-lg hover:bg-button-hover transition-colors"
              >
                测试登录页面
              </a>
              <a 
                href="/login?showRegister=true" 
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                测试登录页面（强制显示注册）
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

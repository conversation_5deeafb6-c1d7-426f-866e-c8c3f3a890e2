import { MetadataRoute } from "next";

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      {
        userAgent: "*",
        allow: "/",
        disallow: [
          "/api/",
          "/private/",
          "/*.json$",
          "/api/resources/", // 添加禁止爬取资源API路由
          "/resources/", // 禁止爬取资源详情页
        ],
      },
      {
        userAgent: "Baiduspider",
        allow: "/",
        disallow: [
          "/api/",
          "/private/",
          "/*.json$",
          "/api/resources/",
          "/resources/", // 百度也禁止爬取资源详情页
        ],
        crawlDelay: 2, // 百度爬虫的抓取延迟
      },
      {
        userAgent: "Googlebot",
        allow: "/",
        disallow: [
          "/api/",
          "/private/",
          "/*.json$",
          "/api/resources/",
          "/resources/", // Google也禁止爬取资源详情页
        ],
      },
      {
        userAgent: "bingbot",
        allow: "/",
        disallow: [
          "/api/",
          "/private/",
          "/*.json$",
          "/api/resources/",
          "/resources/", // Bing也禁止爬取资源详情页
        ],
      },
    ],
    sitemap: "https://pansoo.cn/sitemap.xml",
    host: "https://pansoo.cn",
  };
}

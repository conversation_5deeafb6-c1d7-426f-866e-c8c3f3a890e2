"use client";

import { memo } from "react";
import {
  ClipboardDocumentIcon,
  ClipboardDocumentCheckIcon,
  ArrowTopRightOnSquareIcon,
} from "@heroicons/react/24/outline";

interface CloudLinkButtonProps {
  type: "baidu" | "quark" | "thunder" | "aliyun";
  link: string;
  isLoading: boolean;
  isDisabled: boolean;
  hasError: boolean;
  isUpdated: boolean;
  isCopied: boolean;
  onOpenLink: (event: React.MouseEvent<HTMLAnchorElement>) => void;
  onCopyLink: () => void;
}

/**
 * 网盘链接按钮组件
 * 用于展示单个网盘的链接和复制按钮
 */
const CloudLinkButton = memo(function CloudLinkButton({
  type,
  link,
  isLoading,
  isDisabled,
  hasError,
  isUpdated,
  isCopied,
  onOpenLink,
  onCopyLink,
}: CloudLinkButtonProps) {
  // 获取网盘类型的中文名称
  const getCloudTypeName = (cloudType: string): string => {
    switch (cloudType) {
      case "baidu":
        return "百度网盘";
      case "quark":
        return "夸克网盘";
      case "thunder":
        return "迅雷网盘";
      case "aliyun":
        return "阿里云盘";
      default:
        return cloudType;
    }
  };

  // 获取网盘类型的样式 - 统一配色方案
  const getCloudTypeStyles = () => {
    return {
      openButton: hasError
        ? "bg-red-500 hover:bg-red-600 text-white"
        : "bg-[var(--button-background)] hover:bg-[var(--button-hover)] text-white",
      copyButton: hasError
        ? "bg-red-100 text-red-700 hover:bg-red-200 dark:bg-red-900 dark:text-red-100 dark:hover:bg-red-800"
        : isUpdated
        ? "bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900 dark:text-green-100 dark:hover:bg-green-800"
        : "bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600",
    };
  };

  const styles = getCloudTypeStyles();

  return (
    <div className="flex items-center w-full min-w-0 gap-2 overflow-hidden">
      <span className="font-medium text-xs sm:text-sm min-w-fit truncate">
        {getCloudTypeName(type)}：
      </span>
      <div className="flex flex-1 min-w-0 gap-2">
        <a
          href={link || "#"}
          target="_blank"
          rel="noopener noreferrer"
          onClick={onOpenLink}
          className={`flex-1 min-w-0 py-1.5 px-3 text-xs sm:text-sm ${
            styles.openButton
          } 
                      text-white rounded-sm transition-colors inline-flex items-center justify-center gap-1 text-center truncate
                      ${
                        isLoading || isDisabled
                          ? "opacity-70 cursor-not-allowed hover:bg-opacity-100"
                          : ""
                      }`}
        >
          {isLoading ? (
            <>
              <div className="h-4 w-4 border-t-2 border-white rounded-full animate-spin"></div>
              <span className="truncate">资源校验中</span>
            </>
          ) : (
            <>
              <ArrowTopRightOnSquareIcon className="h-4 w-4" />
              <span className="truncate">进入网盘</span>
            </>
          )}
        </a>
        <button
          type="button"
          onClick={() => {
            if (isDisabled) return;
            onCopyLink();
          }}
          className={`flex-1 min-w-0 py-1.5 px-3 text-xs sm:text-sm ${
            styles.copyButton
          } 
                      rounded-sm transition-colors inline-flex items-center justify-center gap-1 text-center truncate
                      ${
                        isLoading || isDisabled
                          ? "opacity-70 cursor-not-allowed hover:bg-opacity-100"
                          : ""
                      } dark-text-white`}
          disabled={isLoading || isDisabled}
        >
          {isLoading ? (
            <>
              <div className="h-4 w-4 border-t-2 border-gray-500 rounded-full animate-spin"></div>
              <span className="truncate">资源校验中</span>
            </>
          ) : isCopied ? (
            <>
              <ClipboardDocumentCheckIcon className="h-4 w-4" />
              <span className="truncate">已复制</span>
            </>
          ) : (
            <>
              <ClipboardDocumentIcon className="h-4 w-4" />
              <span className="truncate">复制链接</span>
            </>
          )}
        </button>
      </div>
    </div>
  );
});

export default CloudLinkButton;

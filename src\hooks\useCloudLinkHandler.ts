import { useLoadingStore } from "@/store/loadingStore";
import { useToast } from "@/components/ToastProvider";
import {
  processCloudLink,
  openLinkInNewTab,
  getOriginalLink,
} from "@/utils/cloudLinkUtils";
import { useCloudLinkState } from "./useCloudLinkState";

type CloudType = "baidu" | "quark" | "thunder" | "aliyun";
type LinkType = "baiduLink" | "quarkLink" | "thunderLink" | "aliyunLink";

interface UseCloudLinkHandlerProps {
  id: string;
  resourceId?: string;
  searchType?: "local" | "online";
  baiduLink?: string;
  quarkLink?: string;
  aliyunLink?: string;
  thunderLink?: string;
}

/**
 * 自定义Hook：网盘链接处理
 * 管理网盘链接的打开和复制逻辑
 */
export const useCloudLinkHandler = ({
  id,
  resourceId,
  searchType = "local",
  baiduLink,
  quarkLink,
  aliyunLink,
  thunderLink,
}: UseCloudLinkHandlerProps) => {
  const {
    updatedLinks,
    linkUpdated,
    loadingLinks,
    resourceStatus,
    updateLinkState,
    setLoadingState,
    setErrorState,
  } = useCloudLinkState();

  const { setLoading } = useLoadingStore();
  const { showToast } = useToast();

  // 处理链接打开
  const handleOpenLink = async (
    event: React.MouseEvent<HTMLAnchorElement>,
    type: CloudType
  ) => {
    const idToUse = resourceId || id;
    const { isLoading, currentProcessingId } = useLoadingStore.getState();
    const isDisabled = isLoading && currentProcessingId !== idToUse;

    if (isDisabled) {
      event.preventDefault();
      return;
    }

    // 如果已经有错误，直接阻止打开
    if (resourceStatus[type].error) {
      event.preventDefault();
      showToast(resourceStatus[type].message || "资源不可用", "error");
      return;
    }

    event.preventDefault();

    // 如果链接已经更新，使用更新后的链接
    if (linkUpdated[type]) {
      const linkKey = `${type}Link` as
        | "baiduLink"
        | "quarkLink"
        | "thunderLink"
        | "aliyunLink";
      const updatedLink = updatedLinks[linkKey];
      if (updatedLink) {
        openLinkInNewTab(updatedLink);
      } else {
        showToast("链接无效", "error");
      }
      return;
    }

    setLoadingState(type, true);
    setLoading(true, idToUse);

    try {
      const result = await processCloudLink({
        type,
        idToUse,
        searchType,
        baiduLink,
        quarkLink,
        aliyunLink,
        thunderLink,
      });

      if (result.success && result.url) {
        // 检查是否需要更新链接
        const originalLink = getOriginalLink(
          type,
          baiduLink,
          quarkLink,
          aliyunLink,
          thunderLink
        );
        updateLinkState(type, result.url, originalLink);
        openLinkInNewTab(result.url);
      } else {
        setErrorState(type, result.error || "处理链接出错");
        showToast(result.error || "处理链接出错", "error");
      }
    } catch {
      setErrorState(type, "处理链接出错");
      showToast("处理链接出错", "error");
    } finally {
      setLoadingState(type, false);
      setLoading(false);
    }
  };

  // 处理链接复制
  const handleCopyLink = async (
    type: LinkType,
    copyToClipboard: (text: string, type: LinkType) => void
  ) => {
    const idToUse = resourceId || id;
    const { isLoading, currentProcessingId } = useLoadingStore.getState();
    const isDisabled = isLoading && currentProcessingId !== idToUse;

    if (isDisabled) return;

    const typeWithoutLink = type.replace("Link", "") as CloudType;

    // 如果已经有错误，直接提示
    if (resourceStatus[typeWithoutLink].error) {
      showToast(
        resourceStatus[typeWithoutLink].message || "资源不可用",
        "error"
      );
      return;
    }

    // 如果链接已经更新，直接复制更新后的链接
    if (linkUpdated[typeWithoutLink] && updatedLinks[type]) {
      copyToClipboard(updatedLinks[type], type);
      return;
    }

    setLoadingState(typeWithoutLink, true);
    setLoading(true, idToUse);

    try {
      const result = await processCloudLink({
        type: typeWithoutLink,
        idToUse,
        searchType,
        baiduLink,
        quarkLink,
        aliyunLink,
        thunderLink,
      });

      if (result.success && result.url) {
        // 检查是否需要更新链接
        const originalLink = getOriginalLink(
          typeWithoutLink,
          baiduLink,
          quarkLink,
          aliyunLink,
          thunderLink
        );
        updateLinkState(typeWithoutLink, result.url, originalLink);
        copyToClipboard(result.url, type);
      } else {
        setErrorState(typeWithoutLink, result.error || "处理链接出错");
        showToast(result.error || "处理链接出错", "error");
      }
    } catch {
      setErrorState(typeWithoutLink, "处理链接出错");
      showToast("处理链接出错", "error");
    } finally {
      setLoadingState(typeWithoutLink, false);
      setLoading(false);
    }
  };

  return {
    updatedLinks,
    linkUpdated,
    loadingLinks,
    resourceStatus,
    handleOpenLink,
    handleCopyLink,
  };
};

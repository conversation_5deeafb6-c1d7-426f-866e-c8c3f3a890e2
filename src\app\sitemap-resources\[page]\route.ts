import { NextResponse } from "next/server";

export const dynamic = "force-dynamic";

// 注释掉动态站点地图功能
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export async function GET(_request: Request, _context: any) {
  // 返回空的sitemap，表示没有资源
  const emptyXml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"></urlset>`;

  return new NextResponse(emptyXml, {
    headers: { "Content-Type": "application/xml" },
  });

  /* 原代码注释掉
  const params = await context.params;
  const pageNumber = parseInt(params.page, 10);

  if (isNaN(pageNumber) || pageNumber < 1) {
    return new Response("无效的页码", { status: 400 });
  }

  const resources = await getResourcesForSitemap(pageNumber);
  const sitemapXml = generateSitemapXml(resources);

  return new NextResponse(sitemapXml, {
    headers: { "Content-Type": "application/xml" },
  });
  */
}

"use client";

import { useEffect, useState } from "react";
import MarkdownRenderer from "@/components/MarkdownRenderer";
import tutorials from "@/data/tutorials.json";
import Link from "next/link";
import { useParams } from "next/navigation";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";

export default function TutorialPage() {
  const params = useParams();
  const slug =
    typeof params?.slug === "string"
      ? params.slug
      : Array.isArray(params?.slug)
      ? params.slug[0]
      : "";

  const tutorial = tutorials.find((item) => item.slug === slug);

  const [content, setContent] = useState("");

  useEffect(() => {
    if (tutorial) {
      fetch(`/${tutorial.filepath}`)
        .then((res) => (res.ok ? res.text() : ""))
        .then(setContent)
        .catch(() => setContent(""));
    }
  }, [tutorial]);

  if (!tutorial) {
    return (
      <div className="max-w-4xl mx-auto py-12 text-center">
        <h1 className="text-3xl font-bold text-gray-800 mb-4">教程未找到</h1>
        <p className="text-gray-600 mb-8">
          抱歉，您所查找的教程不存在或已被移除。
        </p>
        <Link
          href="/tutorials"
          className="inline-flex items-center text-blue-600 hover:underline"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-1" />
          返回教程列表
        </Link>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <Link
          href="/tutorials"
          className="inline-flex items-center text-blue-600 hover:underline"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-1" />
          返回教程列表
        </Link>
      </div>

      <article className="bg-white rounded-lg shadow-md p-6 md:p-8">
        <header className="mb-8 pb-4 border-b border-gray-100">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            {tutorial.title}
          </h1>
          <p className="text-gray-500">发布于: {tutorial.date}</p>
        </header>

        <MarkdownRenderer content={content} />
      </article>
    </div>
  );
}

/**
 * 应用配置常量
 */

// 站点信息
export const SITE_CONFIG = {
  title: "97盘搜 - 网盘资源搜索平台",
  description:
    "一个专业的网盘搜索引擎,提供百度网盘、夸克网盘、迅雷网盘、阿里云盘资源搜索,涵盖电影、游戏、短剧、软件、小说、学习资料等",
  url: "https://pansoo.cn",
  name: "97盘搜",
};

// API路径
export const API_PATHS = {
  search: "/api/search",
  resource: "/api/resource",
  statistics: "/api/statistics",
};

// 搜索配置
export const SEARCH_CONFIG = {
  types: ["local", "online"] as const,
  defaultType: "online" as const,
  minQueryLength: 2,
  resultsPerPage: 10,
};

// 错误消息
export const ERROR_MESSAGES = {
  searchFailed: "搜索失败，请稍后重试",
  resourceNotFound: "未找到资源",
  networkError: "网络错误，请检查您的网络连接",
};

// 分页配置
export const PAGINATION_CONFIG = {
  defaultPage: 1,
  maxVisiblePages: 5,
};

// 链接处理配置
export const LINK_CONFIG = {
  // 是否启用 get_share 接口调用
  // true: 调用 get_share 接口获取最新链接
  // false: 仅调用 check_resource_status 接口，直接使用返回的 share_url
  enableGetShareAPI: process.env.NEXT_PUBLIC_ENABLE_GET_SHARE_API !== "false",

  // 可以通过环境变量覆盖的网盘类型配置
  // 格式: "baidu,quark" 表示只有百度和夸克使用 get_share 接口
  enableGetShareForTypes: process.env.NEXT_PUBLIC_GET_SHARE_TYPES?.split(
    ","
  ) || ["baidu", "quark"],
};

// 友情链接配置
export const FRIEND_LINKS = {
  // 网盘服务
  cloudServices: [
    {
      name: "百度网盘",
      description:
        "百度公司推出的个人云存储服务，提供文件的网络备份、同步和分享服务",
      url: "https://pan.baidu.com/",
      icon: "/images/baidupan.png",
    },
    {
      name: "夸克网盘",
      description:
        "阿里巴巴旗下智能搜索APP夸克推出的网盘服务，安全可靠的云存储平台",
      url: "https://pan.quark.cn/",
      icon: "/images/quark.png",
    },
    {
      name: "阿里云盘",
      description:
        "阿里云推出的个人网盘产品，提供大容量存储空间和极速上传下载体验",
      url: "https://www.aliyundrive.com/",
      icon: "/images/alipan.png",
    },
    {
      name: "迅雷云盘",
      description: "迅雷公司推出的云存储服务，支持离线下载和多端同步",
      url: "https://pan.xunlei.com/",
      icon: "/images/xunlei.png",
    },
  ],

  // 导航网站
  navigationSites: [
    {
      name: "无瑕导航",
      description: "精选优质网站导航，为您提供便捷的上网入口和实用工具推荐",
      url: "https://www.wuxdh.cn/",
      icon: "https://www.wuxdh.cn/wp-content/uploads/2025/01/%E6%97%A0%E7%91%954%E5%B7%B2%E5%8E%BB%E5%BA%95.png",
    },
    {
      name: "爱搜导航",
      description: "专业的搜索导航网站，汇集各类搜索引擎和实用工具",
      url: "https://www.esoua.cn/",
      icon: "https://www.esoua.cn/favicon.ico", // 使用URL图标示例
    },
    {
      name: "聚资源网",
      description:
        "聚资源网(juzyw.com)，是一家聚合了全网资源的网址导航网站，只需要一个网站就可以拥有全网的优质资源，一个网站在手，全网精彩尽收眼底",
      url: "https://juzyw.com/",
      icon: "https://juzyw.com/wp-content/uploads/2024/11/1731166463-juzyw8.png", // 使用URL图标示例
    },
    {
      name: "Boomcatcher",
      description: "小众有趣网址导航站,尽在虚拟游乐园",
      url: "https://boomcatcher.com",
      icon: "https://www.boomcatcher.com/wp-content/uploads/2024/03/cropped-222-1.png",
    },
  ],

  // 工具网站
  toolSites: [
    {
      name: "97盘搜官方群",
      description: "加入我们的QQ群，获取最新资源信息和使用技巧分享",
      url: "https://qm.qq.com/q/Ehs0DoCFig",
      icon: "/images/qrcode_1746277474741.jpg",
    },
  ],
};

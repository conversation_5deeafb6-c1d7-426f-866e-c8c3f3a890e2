#!/usr/bin/env node

/**
 * 链接配置测试脚本
 * 用于验证环境变量配置是否正确
 */

// 模拟环境变量
const testConfigs = [
  {
    name: "默认配置",
    env: {},
    expected: {
      enableGetShareAPI: true,
      enableGetShareForTypes: ["baidu", "quark"]
    }
  },
  {
    name: "完全禁用 get_share",
    env: {
      NEXT_PUBLIC_ENABLE_GET_SHARE_API: "false"
    },
    expected: {
      enableGetShareAPI: false,
      enableGetShareForTypes: ["baidu", "quark"]
    }
  },
  {
    name: "只启用百度网盘",
    env: {
      NEXT_PUBLIC_ENABLE_GET_SHARE_API: "true",
      NEXT_PUBLIC_GET_SHARE_TYPES: "baidu"
    },
    expected: {
      enableGetShareAPI: true,
      enableGetShareForTypes: ["baidu"]
    }
  },
  {
    name: "启用所有网盘类型",
    env: {
      NEXT_PUBLIC_ENABLE_GET_SHARE_API: "true",
      NEXT_PUBLIC_GET_SHARE_TYPES: "baidu,quark,thunder,aliyun"
    },
    expected: {
      enableGetShareAPI: true,
      enableGetShareForTypes: ["baidu", "quark", "thunder", "aliyun"]
    }
  }
];

function testConfig(config) {
  // 设置环境变量
  const originalEnv = { ...process.env };
  Object.assign(process.env, config.env);

  try {
    // 模拟配置逻辑
    const enableGetShareAPI = process.env.NEXT_PUBLIC_ENABLE_GET_SHARE_API !== "false";
    const enableGetShareForTypes = process.env.NEXT_PUBLIC_GET_SHARE_TYPES?.split(",") || ["baidu", "quark"];

    const actual = {
      enableGetShareAPI,
      enableGetShareForTypes
    };

    // 验证结果
    const isCorrect = 
      actual.enableGetShareAPI === config.expected.enableGetShareAPI &&
      JSON.stringify(actual.enableGetShareForTypes.sort()) === JSON.stringify(config.expected.enableGetShareForTypes.sort());

    console.log(`\n测试: ${config.name}`);
    console.log(`环境变量:`, config.env);
    console.log(`期望结果:`, config.expected);
    console.log(`实际结果:`, actual);
    console.log(`测试结果: ${isCorrect ? '✅ 通过' : '❌ 失败'}`);

    return isCorrect;
  } finally {
    // 恢复环境变量
    process.env = originalEnv;
  }
}

function runTests() {
  console.log("开始测试链接配置...\n");
  
  let passedTests = 0;
  const totalTests = testConfigs.length;

  testConfigs.forEach(config => {
    if (testConfig(config)) {
      passedTests++;
    }
  });

  console.log(`\n测试完成: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log("🎉 所有测试通过！");
    process.exit(0);
  } else {
    console.log("❌ 部分测试失败");
    process.exit(1);
  }
}

// 运行测试
runTests();

import React, { useState, useEffect } from 'react';

const DebugNotificationPage: React.FC = () => {
  const [notificationStatus, setNotificationStatus] = useState('正在检查公告状态...');
  const [yamlContent, setYamlContent] = useState('');
  const [dismissedStatus, setDismissedStatus] = useState(false);
  const [resetKeyStatus, setResetKeyStatus] = useState('');
  const [lastRefresh, setLastRefresh] = useState(new Date());

  const refreshData = () => {
    // 更新刷新时间
    setLastRefresh(new Date());
    
    // 检查localStorage状态
    const isDismissed = localStorage.getItem('notification_dismissed') === 'true';
    setDismissedStatus(isDismissed);
    
    // 获取重置键
    const resetKey = localStorage.getItem('notification_reset_key') || '无';
    setResetKeyStatus(resetKey);

    // 获取YAML内容（添加时间戳避免缓存）
    fetch(`/notification.yaml?t=${new Date().getTime()}`)
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP 错误: ${response.status}`);
        }
        return response.text();
      })
      .then(text => {
        setYamlContent(text);
      })
      .catch(error => {
        setYamlContent(`获取失败: ${error.message}`);
      });

    // 获取调试信息
    setTimeout(() => {
      const debugElement = document.getElementById('notification-debug-info');
      if (debugElement) {
        setNotificationStatus(debugElement.textContent || '无法获取调试信息');
      } else {
        setNotificationStatus('未找到调试元素，可能组件未正确加载');
      }
    }, 500);
  };

  useEffect(() => {
    refreshData();
  }, []);

  const resetDismissed = () => {
    localStorage.removeItem('notification_dismissed');
    localStorage.removeItem('notification_reset_key');
    setDismissedStatus(false);
    setResetKeyStatus('无');
    
    // @ts-ignore
    if (window.__reloadNotification) {
      // @ts-ignore
      window.__reloadNotification();
      setTimeout(refreshData, 500);
    } else {
      setNotificationStatus('无法重载公告，请刷新页面');
    }
  };

  const forceShowNotification = () => {
    // @ts-ignore
    if (window.__forceShowNotification) {
      // @ts-ignore
      window.__forceShowNotification();
      setTimeout(refreshData, 500);
    } else {
      resetDismissed();
      // 刷新页面以确保公告显示
      window.location.reload();
    }
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">公告调试页面</h1>
      
      <div className="mb-4 flex justify-between items-center">
        <span className="text-sm text-gray-500">最后刷新: {lastRefresh.toLocaleTimeString()}</span>
        <button 
          onClick={refreshData}
          className="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 text-sm"
        >
          刷新数据
        </button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="border rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-4">公告状态</h2>
          <div className="text-sm bg-gray-100 p-4 rounded whitespace-pre-wrap mb-4 max-h-64 overflow-auto">
            {notificationStatus}
          </div>
          
          <div className="mb-4 space-y-2">
            <div className="flex items-center space-x-2">
              <span className="font-medium">已关闭状态:</span>
              <span className={dismissedStatus ? 'text-red-500' : 'text-green-500'}>
                {dismissedStatus ? '已关闭' : '未关闭'}
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              <span className="font-medium">重置键:</span>
              <span className="text-blue-500">{resetKeyStatus}</span>
            </div>
          </div>
          
          <div className="flex flex-wrap gap-2">
            <button 
              onClick={resetDismissed}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              重置已关闭状态
            </button>
            
            <button 
              onClick={forceShowNotification}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
            >
              强制显示公告
            </button>
            
            <button 
              onClick={() => localStorage.clear()}
              className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
            >
              清除所有本地存储
            </button>
          </div>
        </div>
        
        <div className="border rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-4">公告配置内容</h2>
          <pre className="text-sm bg-gray-100 p-4 rounded overflow-auto h-64">
            {yamlContent}
          </pre>
        </div>
      </div>

      <div className="mt-6 border rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-4">常见问题排查</h2>
        <ul className="list-disc pl-6 space-y-2">
          <li>
            <strong>公告未显示可能的原因:</strong>
            <ul className="list-circle pl-6 mt-1">
              <li>YAML 配置文件无法访问</li>
              <li>YAML 格式错误或包含重复字段</li>
              <li>enabled 设置为 false</li>
              <li>日期格式不正确或不在有效时间范围内</li>
              <li>用户已关闭过公告</li>
            </ul>
          </li>
          <li>
            <strong>调试方法:</strong>
            <ul className="list-circle pl-6 mt-1">
              <li>在浏览器开发者工具的控制台中，查看是否有错误信息</li>
              <li>检查网络请求，确认notification.yaml是否正确加载</li>
              <li>确认YAML文件格式是否正确，没有重复的键</li>
              <li>使用上方的&quot;强制显示公告&quot;按钮测试</li>
              <li>如果问题仍未解决，尝试清除浏览器缓存并刷新页面</li>
            </ul>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default DebugNotificationPage; 